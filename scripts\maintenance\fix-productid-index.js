import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { logger } from '../src/utils/logger.js';
import { connectDatabase, disconnectDatabase } from '../src/database/connection.js';

// Carrega variáveis de ambiente
dotenv.config();

/**
 * Script para corrigir o erro E11000 duplicate key error no campo productId
 * 
 * Este script:
 * 1. Conecta ao banco de dados
 * 2. Lista todos os índices da coleção products
 * 3. Remove o índice órfão productId_1 se existir
 * 4. Verifica a integridade dos dados
 * 5. Gera relatório das correções aplicadas
 */

async function fixProductIdIndexError() {
    try {
        console.log('🔧 Iniciando correção do erro de índice productId...\n');
        
        // Conecta ao banco de dados
        await connectDatabase();
        
        const db = mongoose.connection.db;
        const productsCollection = db.collection('products');
        
        // 1. Lista todos os índices atuais
        console.log('📋 Listando índices atuais da coleção products:');
        const indexes = await productsCollection.indexes();
        
        console.log('Índices encontrados:');
        indexes.forEach((index, i) => {
            console.log(`  ${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
        });
        console.log('');
        
        // 2. Verifica se existe o índice problemático productId_1
        const problematicIndex = indexes.find(index => index.name === 'productId_1');
        
        if (problematicIndex) {
            console.log('⚠️  Índice problemático encontrado: productId_1');
            console.log(`   Definição: ${JSON.stringify(problematicIndex.key)}`);
            console.log(`   Único: ${problematicIndex.unique || false}`);
            
            // Remove o índice órfão
            console.log('🗑️  Removendo índice órfão productId_1...');
            await productsCollection.dropIndex('productId_1');
            console.log('✅ Índice productId_1 removido com sucesso!\n');
        } else {
            console.log('✅ Nenhum índice problemático productId_1 encontrado.\n');
        }
        
        // 3. Verifica se existem documentos com campo productId
        console.log('🔍 Verificando documentos com campo productId...');
        const docsWithProductId = await productsCollection.countDocuments({ productId: { $exists: true } });
        
        if (docsWithProductId > 0) {
            console.log(`⚠️  Encontrados ${docsWithProductId} documentos com campo productId órfão.`);
            
            // Lista alguns exemplos
            const examples = await productsCollection.find(
                { productId: { $exists: true } },
                { name: 1, productId: 1, _id: 1 }
            ).limit(5).toArray();
            
            console.log('Exemplos de documentos com productId:');
            examples.forEach((doc, i) => {
                console.log(`  ${i + 1}. ID: ${doc._id}, Nome: ${doc.name}, productId: ${doc.productId}`);
            });
            
            // Remove o campo órfão de todos os documentos
            console.log('\n🧹 Removendo campo productId órfão de todos os documentos...');
            const updateResult = await productsCollection.updateMany(
                { productId: { $exists: true } },
                { $unset: { productId: "" } }
            );
            
            console.log(`✅ Campo productId removido de ${updateResult.modifiedCount} documentos.\n`);
        } else {
            console.log('✅ Nenhum documento com campo productId órfão encontrado.\n');
        }
        
        // 4. Verifica a integridade dos dados após as correções
        console.log('🔍 Verificando integridade dos dados...');
        
        const totalProducts = await productsCollection.countDocuments();
        const productsWithRequiredFields = await productsCollection.countDocuments({
            name: { $exists: true, $ne: null },
            price: { $exists: true, $ne: null },
            storeId: { $exists: true, $ne: null },
            createdBy: { $exists: true, $ne: null }
        });
        
        console.log(`Total de produtos: ${totalProducts}`);
        console.log(`Produtos com campos obrigatórios: ${productsWithRequiredFields}`);
        
        if (totalProducts === productsWithRequiredFields) {
            console.log('✅ Todos os produtos possuem os campos obrigatórios.\n');
        } else {
            console.log(`⚠️  ${totalProducts - productsWithRequiredFields} produtos podem ter campos obrigatórios faltando.\n`);
        }
        
        // 5. Lista os índices finais
        console.log('📋 Índices finais da coleção products:');
        const finalIndexes = await productsCollection.indexes();
        finalIndexes.forEach((index, i) => {
            console.log(`  ${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
        });
        
        console.log('\n✅ Correção do erro de índice productId concluída com sucesso!');
        console.log('🎉 O erro E11000 duplicate key error deve estar resolvido.');
        
    } catch (error) {
        console.error('❌ Erro durante a correção:', error);
        throw error;
    } finally {
        await disconnectDatabase();
    }
}

// Executa o script se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
    fixProductIdIndexError()
        .then(() => {
            console.log('\n🎯 Script executado com sucesso!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Erro fatal:', error);
            process.exit(1);
        });
}

export { fixProductIdIndexError };
