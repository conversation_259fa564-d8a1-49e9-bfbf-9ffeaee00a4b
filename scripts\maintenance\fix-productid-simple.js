import dotenv from 'dotenv';
import mongoose from 'mongoose';

// Carrega variáveis de ambiente
dotenv.config();

async function fixProductIdIndex() {
    try {
        console.log('🔧 Iniciando correção do índice productId...');
        
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB');
        
        const db = mongoose.connection.db;
        const productsCollection = db.collection('products');
        
        // 1. Lista índices atuais
        console.log('📋 Índices atuais:');
        const indexes = await productsCollection.indexes();
        indexes.forEach(index => {
            console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
        });
        
        // 2. Verifica se existe o índice problemático
        const hasProductIdIndex = indexes.some(index => index.name === 'productId_1');
        
        if (hasProductIdIndex) {
            console.log('⚠️  Encontrado índice problemático: productId_1');
            
            // Remove o índice
            console.log('🗑️  Removendo índice productId_1...');
            await productsCollection.dropIndex('productId_1');
            console.log('✅ Índice productId_1 removido!');
        } else {
            console.log('✅ Índice productId_1 não encontrado');
        }
        
        // 3. Verifica documentos com campo productId
        const docsWithProductId = await productsCollection.countDocuments({ productId: { $exists: true } });
        console.log(`🔍 Documentos com campo productId: ${docsWithProductId}`);
        
        if (docsWithProductId > 0) {
            console.log('🧹 Removendo campo productId dos documentos...');
            const result = await productsCollection.updateMany(
                { productId: { $exists: true } },
                { $unset: { productId: "" } }
            );
            console.log(`✅ Campo productId removido de ${result.modifiedCount} documentos`);
        }
        
        // 4. Verifica outros campos órfãos
        const orphanFields = ['isActive', 'featured', 'salesCount'];
        for (const field of orphanFields) {
            const count = await productsCollection.countDocuments({ [field]: { $exists: true } });
            if (count > 0) {
                console.log(`⚠️  Campo órfão '${field}' encontrado em ${count} documentos`);
            }
        }
        
        // 5. Lista índices finais
        console.log('📋 Índices finais:');
        const finalIndexes = await productsCollection.indexes();
        finalIndexes.forEach(index => {
            console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
        });
        
        await mongoose.disconnect();
        console.log('✅ Correção concluída com sucesso!');
        
    } catch (error) {
        console.error('❌ Erro:', error);
        process.exit(1);
    }
}

fixProductIdIndex();
