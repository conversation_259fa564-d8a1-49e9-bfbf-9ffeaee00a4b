import { Events } from 'discord.js';
import { logger } from '../utils/logging/logger.js';

export default {
    name: Events.MessageCreate,
    async execute(message) {
        try {
            // Ignora mensagens do próprio bot
            if (message.author.bot) return;

            // Log apenas mensagens que mencionam o bot ou contêm palavras-chave importantes
            const botMentioned = message.mentions.has(message.client.user);
            const hasKeywords = /\b(erro|bug|problema|ajuda|suporte)\b/i.test(message.content);
            
            if (botMentioned || hasKeywords) {
                await logger.event(`Mensagem relevante detectada`, {
                    guildId: message.guild?.id,
                    userId: message.author.id,
                    channel: message.channel.id
                }, {
                    user: message.author.tag,
                    guild: message.guild?.name || 'DM',
                    channel: message.channel.name || 'DM',
                    botMentioned,
                    hasKeywords,
                    messageLength: message.content.length,
                    hasAttachments: message.attachments.size > 0
                });
            }

            // Log de segurança para mensagens suspeitas
            const suspiciousPatterns = [
                /discord\.gg\/\w+/i,  // Links de convite
                /https?:\/\/[^\s]+/g,  // URLs em geral
                /@everyone|@here/i     // Menções em massa
            ];

            const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(message.content));
            
            if (isSuspicious && !message.member?.permissions.has('ADMINISTRATOR')) {
                await logger.security(`Mensagem suspeita detectada`, {
                    guildId: message.guild?.id,
                    userId: message.author.id,
                    channel: message.channel.id
                }, {
                    user: message.author.tag,
                    guild: message.guild?.name || 'DM',
                    channel: message.channel.name || 'DM',
                    contentPreview: message.content.substring(0, 100),
                    hasInvites: /discord\.gg\/\w+/i.test(message.content),
                    hasUrls: /https?:\/\/[^\s]+/g.test(message.content),
                    hasMassMentions: /@everyone|@here/i.test(message.content)
                });
            }

        } catch (error) {
            await logger.logStructured('ERROR', 'EVENT', 'Erro no evento messageCreate', {
                guildId: message.guild?.id,
                userId: message.author?.id,
                channel: message.channel?.id
            }, {
                error: error.message,
                stack: error.stack
            });
        }
    }
};
