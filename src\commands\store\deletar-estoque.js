import { SlashCommandBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder, ActionRowBuilder } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import Store from '../../models/Store.js';

export default {
    data: new SlashCommandBuilder()
        .setName('deletar-estoque')
        .setDescription('Remove itens específicos do estoque (apenas administradores)'),

    async execute(interaction) {
        try {
            // Verificação de permissões
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Você não tem permissão para usar este comando. Apenas administradores podem gerenciar estoque.',
                    ephemeral: true
                });
            }

            const guildId = interaction.guild.id;

            // Busca lojas ativas no servidor
            const stores = await Store.find({ 
                guildId, 
                isActive: true 
            }).sort({ name: 1 });

            if (stores.length === 0) {
                return await interaction.reply({
                    content: '❌ Não há lojas ativas neste servidor. Crie uma loja primeiro usando `/criar-loja`.',
                    ephemeral: true
                });
            }

            // Cria o select menu com as lojas disponíveis
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('delete_stock_store_select')
                .setPlaceholder('Selecione a loja do produto...')
                .setMinValues(1)
                .setMaxValues(1);

            // Adiciona as lojas como opções
            for (const store of stores) {
                // Busca informações do canal
                const channel = interaction.guild.channels.cache.get(store.channelId);
                const channelInfo = channel ? `#${channel.name}` : `Canal não encontrado`;
                
                selectMenu.addOptions(
                    new StringSelectMenuOptionBuilder()
                        .setLabel(store.name)
                        .setDescription(`${channelInfo} • Criada em ${store.createdAt.toLocaleDateString('pt-BR')}`)
                        .setValue(store._id.toString())
                        .setEmoji('🏪')
                );
            }

            // Adiciona opção de cancelar
            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel('❌ Cancelar')
                    .setDescription('Cancelar a remoção de estoque')
                    .setValue('cancel')
                    .setEmoji('❌')
            );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.reply({
                content: '🗑️ **Deletar Estoque**\n\nSelecione a loja que contém o produto para remover estoque:',
                components: [row],
                ephemeral: true
            });

            logger.info(`Comando deletar-estoque executado por ${interaction.user.tag} em ${interaction.guild.name}`);

        } catch (error) {
            logger.error('Erro no comando deletar-estoque:', error);
            
            const errorMessage = {
                content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
                ephemeral: true
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    }
};
