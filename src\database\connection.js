import mongoose from 'mongoose';
import { logger } from '../utils/logging/logger.js';

/**
 * Conecta ao banco de dados MongoDB
 */
export async function connectDatabase() {
    const startTime = Date.now();

    try {
        const mongoUri = process.env.MONGODB_URI;

        if (!mongoUri) {
            throw new Error('MONGODB_URI não está definida nas variáveis de ambiente');
        }

        await logger.database('Iniciando conexão com MongoDB', {}, {
            uri: mongoUri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'), // Oculta credenciais
            environment: process.env.NODE_ENV || 'production'
        });

        // Configurações de conexão
        const options = {
            maxPoolSize: 10, // Máximo de 10 conexões simultâneas
            serverSelectionTimeoutMS: 5000, // Timeout de 5 segundos
            socketTimeoutMS: 45000, // Timeout de socket de 45 segundos
        };

        await mongoose.connect(mongoUri, options);

        const connectionTime = Date.now() - startTime;

        await logger.database('✅ Conectado ao MongoDB com sucesso', {}, {
            connectionTime: `${connectionTime}ms`,
            database: mongoose.connection.db.databaseName,
            host: mongoose.connection.host,
            port: mongoose.connection.port,
            readyState: mongoose.connection.readyState
        });

        // Event listeners para monitorar a conexão com logs estruturados
        mongoose.connection.on('error', async (error) => {
            await logger.logStructured('ERROR', 'DATABASE', 'Erro na conexão MongoDB', {}, {
                error: error.message,
                stack: error.stack,
                code: error.code
            });
        });

        mongoose.connection.on('disconnected', async () => {
            await logger.logStructured('WARN', 'DATABASE', 'MongoDB desconectado', {}, {
                timestamp: new Date().toISOString(),
                readyState: mongoose.connection.readyState
            });
        });

        mongoose.connection.on('reconnected', async () => {
            await logger.database('🔄 MongoDB reconectado', {}, {
                timestamp: new Date().toISOString(),
                readyState: mongoose.connection.readyState
            });
        });

        mongoose.connection.on('connected', async () => {
            await logger.database('MongoDB conectado', {}, {
                database: mongoose.connection.db.databaseName,
                host: mongoose.connection.host,
                port: mongoose.connection.port
            });
        });

        mongoose.connection.on('connecting', async () => {
            await logger.database('Conectando ao MongoDB...', {}, {
                timestamp: new Date().toISOString()
            });
        });

    } catch (error) {
        const connectionTime = Date.now() - startTime;

        await logger.logStructured('ERROR', 'DATABASE', 'Erro ao conectar ao MongoDB', {}, {
            error: error.message,
            stack: error.stack,
            connectionTime: `${connectionTime}ms`,
            code: error.code
        });
        throw error;
    }
}

/**
 * Desconecta do banco de dados
 */
export async function disconnectDatabase() {
    try {
        await logger.database('Desconectando do MongoDB...', {}, {
            readyState: mongoose.connection.readyState
        });

        await mongoose.disconnect();

        await logger.database('✅ Desconectado do MongoDB', {}, {
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        await logger.logStructured('ERROR', 'DATABASE', 'Erro ao desconectar do MongoDB', {}, {
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

/**
 * Middleware para logging de operações do Mongoose
 */
export function setupDatabaseLogging() {
    // Log de queries lentas
    mongoose.set('debug', async function(collectionName, method, query, _doc, options) {
        if (process.env.NODE_ENV === 'development' || process.env.LOG_LEVEL === 'DEBUG') {
            await logger.database(`Query executada: ${method}`, {}, {
                collection: collectionName,
                method,
                query: JSON.stringify(query),
                options: JSON.stringify(options)
            });
        }
    });

    // Plugin global para logging de operações
    mongoose.plugin(function(schema, _options) {
        // Log de operações de save
        schema.post('save', async function(doc) {
            try {
                // Obtém o nome da coleção de forma segura
                const collectionName = this.constructor?.collection?.name ||
                                     this.collection?.name ||
                                     doc.constructor?.collection?.name ||
                                     schema.options?.collection ||
                                     'unknown';

                await logger.database(`Documento salvo`, {}, {
                    collection: collectionName,
                    operation: 'save',
                    documentId: doc._id?.toString()
                });
            } catch (error) {
                // Log do erro sem interromper a operação
                console.error('Erro no logging de save:', error.message);
            }
        });

        // Log de operações de remove
        schema.post('remove', async function(doc) {
            try {
                // Obtém o nome da coleção de forma segura
                const collectionName = this.constructor?.collection?.name ||
                                     this.collection?.name ||
                                     doc.constructor?.collection?.name ||
                                     schema.options?.collection ||
                                     'unknown';

                await logger.database(`Documento removido`, {}, {
                    collection: collectionName,
                    operation: 'remove',
                    documentId: doc._id?.toString()
                });
            } catch (error) {
                // Log do erro sem interromper a operação
                console.error('Erro no logging de remove:', error.message);
            }
        });

        // Log de operações de update
        schema.post(['updateOne', 'updateMany', 'findOneAndUpdate'], async function(result) {
            try {
                // Obtém o nome da coleção de forma segura
                let collectionName = 'unknown';

                if (this.getQuery && this.getQuery().constructor?.collection?.name) {
                    collectionName = this.getQuery().constructor.collection.name;
                } else if (this.model?.collection?.name) {
                    collectionName = this.model.collection.name;
                } else if (schema.options?.collection) {
                    collectionName = schema.options.collection;
                }

                await logger.database(`Documento(s) atualizado(s)`, {}, {
                    collection: collectionName,
                    operation: 'update',
                    modifiedCount: result.modifiedCount || result.nModified || 'unknown'
                });
            } catch (error) {
                // Log do erro sem interromper a operação
                console.error('Erro no logging de update:', error.message);
            }
        });
    });
}
