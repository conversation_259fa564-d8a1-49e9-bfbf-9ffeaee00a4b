import { Events } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import { botLogger } from '../../utils/logging/botLogger.js';

export default {
    name: Events.ClientReady,
    once: true,
    async execute(client) {
        try {
            const guildCount = client.guilds.cache.size;
            const userCount = client.users.cache.size;
            const commandCount = client.commands.size;

            await logger.system(`✅ Bot logado como ${client.user.tag}`, {}, {
                guilds: guildCount,
                users: userCount,
                commands: commandCount
            });

            // Log para cada servidor conectado
            for (const guild of client.guilds.cache.values()) {
                await logger.system(`🔗 Conectado ao servidor: ${guild.name}`, {
                    guildId: guild.id
                }, {
                    memberCount: guild.memberCount,
                    owner: guild.ownerId
                });
            }

            // Define a atividade do bot
            client.user.setActivity('Gerenciando a loja', { type: 'WATCHING' });
            await logger.system('🎮 Atividade do bot definida');

            if (process.env.NODE_ENV === 'development') {
                await logger.system('🔧 Bot rodando em modo de desenvolvimento');
            }

            // Log de inicialização completa
            await logger.system('🎉 Bot inicializado com sucesso', {}, {
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage()
            });

        } catch (error) {
            await logger.logStructured('ERROR', 'SYSTEM', 'Erro no evento ready', {}, {
                error: error.message,
                stack: error.stack
            });
        }
    }
};
