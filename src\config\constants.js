/**
 * Constantes e configurações do bot
 */

export const BOT_CONFIG = {
    // Configurações gerais
    PREFIX: process.env.BOT_PREFIX || '!',
    NODE_ENV: process.env.NODE_ENV || 'development',
    OWNER_ID: process.env.BOT_OWNER_ID, // Discord ID do dono do bot
    
    // Configurações de paginação
    ITEMS_PER_PAGE: 5,
    MAX_EMBED_FIELDS: 25,
    
    // Limites de caracteres
    MAX_EMBED_DESCRIPTION: 4096,
    MAX_EMBED_FIELD_VALUE: 1024,
    MAX_EMBED_TITLE: 256,
    
    // Timeouts (em milissegundos)
    INTERACTION_TIMEOUT: 15 * 60 * 1000, // 15 minutos
    BUTTON_TIMEOUT: 5 * 60 * 1000,       // 5 minutos
    MODAL_TIMEOUT: 10 * 60 * 1000,       // 10 minutos
    
    // Configurações da loja
    STORE: {
        DEFAULT_CURRENCY: 'BRL',
        CURRENCY_SYMBOL: 'R$',
        MAX_CART_ITEMS: 10,
        MAX_QUANTITY_PER_ITEM: 99,
        MIN_ORDER_VALUE: 1.00,
        MAX_ORDER_VALUE: 10000.00
    }
};

export const COLORS = {
    // Cores principais
    PRIMARY: '#0099ff',
    SECONDARY: '#6c757d',
    SUCCESS: '#00ff00',
    WARNING: '#ffff00',
    ERROR: '#ff0000',
    DANGER: '#dc3545',
    INFO: '#00ffff',
    
    // Cores específicas
    MONEY: '#85bb65',
    PREMIUM: '#f1c40f',
    ADMIN: '#e74c3c',
    MODERATOR: '#3498db'
};

export const EMOJIS = {
    // Emojis gerais
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    INFO: 'ℹ️',
    LOADING: '⏳',

    // Emojis da loja
    MONEY: '💰',
    CART: '🛒',
    PRODUCT: '📦',
    DIGITAL: '💾',
    PHYSICAL: '📦',
    SERVICE: '🛠️',
    SUBSCRIPTION: '🔄',

    // Emojis de navegação
    PREVIOUS: '◀️',
    NEXT: '▶️',
    REFRESH: '🔄',
    HOME: '🏠',
    BACK: '↩️',

    // Emojis de status
    ONLINE: '🟢',
    OFFLINE: '🔴',
    IDLE: '🟡',
    DND: '⛔'
};

/**
 * Obtém emojis personalizados para um servidor específico
 * @param {string} guildId - ID do servidor
 * @returns {Promise<Object>} Objeto com emojis personalizados ou padrões
 */
export async function getEmojisForGuild(guildId) {
    try {
        // Importação dinâmica para evitar dependência circular
        const { emojiManager } = await import('../utils/emojiManager.js');
        return await emojiManager.getAllEmojis(guildId);
    } catch (error) {
        // Em caso de erro, retorna emojis padrões
        console.error('Erro ao carregar emojis personalizados:', error);
        return EMOJIS;
    }
}

/**
 * Obtém um emoji específico para um servidor
 * @param {string} guildId - ID do servidor
 * @param {string} emojiKey - Chave do emoji
 * @returns {Promise<string>} Emoji personalizado ou padrão
 */
export async function getEmojiForGuild(guildId, emojiKey) {
    try {
        // Importação dinâmica para evitar dependência circular
        const { emojiManager } = await import('../utils/emojiManager.js');
        return await emojiManager.getEmoji(guildId, emojiKey);
    } catch (error) {
        // Em caso de erro, retorna emoji padrão
        console.error(`Erro ao carregar emoji ${emojiKey}:`, error);
        return EMOJIS[emojiKey] || '❓';
    }
}

export const PERMISSIONS = {
    // Permissões do Discord
    ADMINISTRATOR: 'Administrator',
    MANAGE_GUILD: 'ManageGuild',
    MANAGE_CHANNELS: 'ManageChannels',
    MANAGE_MESSAGES: 'ManageMessages',
    KICK_MEMBERS: 'KickMembers',
    BAN_MEMBERS: 'BanMembers',
    
    // Níveis de acesso customizados
    LEVELS: {
        USER: 0,
        MODERATOR: 1,
        ADMIN: 2,
        OWNER: 3
    }
};

export const DATABASE = {
    // Configurações do MongoDB
    CONNECTION_TIMEOUT: 30000,
    MAX_POOL_SIZE: 10,
    MIN_POOL_SIZE: 1,
    
    // Configurações de cache
    CACHE_TTL: 5 * 60 * 1000, // 5 minutos
    
    // Limites de consulta
    MAX_QUERY_LIMIT: 100,
    DEFAULT_QUERY_LIMIT: 20
};

export const VALIDATION = {
    // Validações de usuário
    USERNAME: {
        MIN_LENGTH: 2,
        MAX_LENGTH: 32
    },
    
    // Validações de produto
    PRODUCT_NAME: {
        MIN_LENGTH: 3,
        MAX_LENGTH: 100
    },
    PRODUCT_DESCRIPTION: {
        MIN_LENGTH: 10,
        MAX_LENGTH: 1000
    },
    PRODUCT_PRICE: {
        MIN: 0.01,
        MAX: 999999.99
    },
    
    // Validações de pedido
    ORDER_NOTES: {
        MAX_LENGTH: 500
    },

    // Validações de estoque
    STOCK_CONTENT: {
        MAX_TOTAL_LENGTH: 4000,
        MAX_LINE_LENGTH: 500
    }
};

export const MESSAGES = {
    // Mensagens de erro comuns
    ERRORS: {
        GENERIC: 'Ocorreu um erro inesperado. Tente novamente mais tarde.',
        PERMISSION_DENIED: 'Você não tem permissão para executar esta ação.',
        USER_NOT_FOUND: 'Usuário não encontrado.',
        PRODUCT_NOT_FOUND: 'Produto não encontrado.',
        INSUFFICIENT_BALANCE: 'Saldo insuficiente.',
        OUT_OF_STOCK: 'Produto fora de estoque.',
        INVALID_QUANTITY: 'Quantidade inválida.',
        DATABASE_ERROR: 'Erro de conexão com o banco de dados.'
    },
    
    // Mensagens de sucesso
    SUCCESS: {
        COMMAND_EXECUTED: 'Comando executado com sucesso!',
        PRODUCT_ADDED: 'Produto adicionado com sucesso!',
        ORDER_CREATED: 'Pedido criado com sucesso!',
        PAYMENT_APPROVED: 'Pagamento aprovado!',
        BALANCE_UPDATED: 'Saldo atualizado com sucesso!'
    },
    
    // Mensagens informativas
    INFO: {
        PROCESSING: 'Processando sua solicitação...',
        PLEASE_WAIT: 'Por favor, aguarde...',
        NO_RESULTS: 'Nenhum resultado encontrado.',
        EMPTY_CART: 'Seu carrinho está vazio.',
        MAINTENANCE: 'Sistema em manutenção. Tente novamente mais tarde.'
    }
};
