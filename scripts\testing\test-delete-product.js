import dotenv from 'dotenv';
dotenv.config();

import { connectDatabase, disconnectDatabase } from '../src/database/connection.js';
import Store from '../src/models/Store.js';
import Product from '../src/models/Product.js';
import StockItem from '../src/models/StockItem.js';
import { logger } from '../src/utils/logger.js';

/**
 * Script para testar a funcionalidade de deletar produto
 * Verifica se os produtos podem ser encontrados e deletados corretamente
 */

async function testDeleteProductFunctionality() {
    try {
        console.log('🔍 Iniciando teste da funcionalidade de deletar produto...\n');

        // Conecta ao banco de dados
        await connectDatabase();

        // 1. Busca uma loja ativa para teste
        console.log('📋 Buscando lojas ativas...');
        const stores = await Store.find({ isActive: true }).limit(5);
        
        if (stores.length === 0) {
            console.log('❌ Nenhuma loja ativa encontrada para teste');
            return;
        }

        console.log(`✅ Encontradas ${stores.length} lojas ativas:`);
        stores.forEach(store => {
            console.log(`   - ${store.name} (ID: ${store._id})`);
        });

        // 2. Para cada loja, busca produtos usando a mesma lógica do comando delete
        for (const store of stores) {
            console.log(`\n🏪 Testando loja: ${store.name}`);
            
            // Simula a busca de produtos como no handleDeleteProductStoreSelect
            const products = await Product.find({ 
                storeId: store._id, 
                status: { $ne: 'discontinued' }
            }).sort({ name: 1 });

            console.log(`   📦 Produtos encontrados: ${products.length}`);
            
            if (products.length > 0) {
                console.log('   Produtos disponíveis para deletar:');
                products.forEach((product, index) => {
                    console.log(`      ${index + 1}. ${product.name} (Status: ${product.status}, ID: ${product._id})`);
                });

                // 3. Testa a busca individual de produto (como no handleDeleteProductProductSelect)
                const firstProduct = products[0];
                console.log(`\n   🔍 Testando busca individual do produto: ${firstProduct.name}`);
                
                const foundProduct = await Product.findOne({ 
                    _id: firstProduct._id, 
                    status: { $ne: 'discontinued' } 
                }).populate('storeId');

                if (foundProduct) {
                    console.log(`   ✅ Produto encontrado com sucesso!`);
                    console.log(`      - Nome: ${foundProduct.name}`);
                    console.log(`      - Status: ${foundProduct.status}`);
                    console.log(`      - Loja: ${foundProduct.storeId.name}`);
                    
                    // 4. Conta itens de estoque
                    const stockCount = await StockItem.countDocuments({ 
                        productId: foundProduct._id, 
                        status: 'available' 
                    });
                    console.log(`      - Itens de estoque disponíveis: ${stockCount}`);

                    // 5. Simula o processo de deleção (SEM EXECUTAR)
                    console.log(`\n   🧪 Simulando processo de deleção (sem executar):`);
                    console.log(`      - Deletaria ${stockCount} itens de estoque`);
                    console.log(`      - Mudaria status do produto de '${foundProduct.status}' para 'inactive'`);
                    console.log(`      - Decrementaria contador de produtos da loja`);
                    
                } else {
                    console.log(`   ❌ Produto não encontrado na busca individual!`);
                }
            } else {
                console.log('   📭 Nenhum produto disponível para deletar nesta loja');
            }
        }

        // 6. Testa busca de produtos com diferentes status
        console.log(`\n📊 Análise de produtos por status:`);
        const statusCounts = await Product.aggregate([
            { $group: { _id: '$status', count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]);

        statusCounts.forEach(item => {
            console.log(`   - ${item._id || 'undefined'}: ${item.count} produtos`);
        });

        // 7. Verifica se há produtos com status problemático
        console.log(`\n🔍 Verificando produtos com status problemático...`);
        const problematicProducts = await Product.find({
            status: { $nin: ['active', 'inactive', 'out_of_stock', 'discontinued'] }
        });

        if (problematicProducts.length > 0) {
            console.log(`⚠️  Encontrados ${problematicProducts.length} produtos com status inválido:`);
            problematicProducts.forEach(product => {
                console.log(`   - ${product.name}: status = '${product.status}'`);
            });
        } else {
            console.log(`✅ Todos os produtos têm status válidos`);
        }

        console.log('\n✅ Teste da funcionalidade de deletar produto concluído!');
        console.log('\n📋 Resumo dos resultados:');
        console.log(`   - Lojas ativas encontradas: ${stores.length}`);
        console.log(`   - Produtos testados com sucesso`);
        console.log(`   - Busca individual funcionando corretamente`);
        console.log(`   - Lógica de deleção validada`);

    } catch (error) {
        console.error('❌ Erro durante o teste:', error);
        console.error('Stack trace:', error.stack);
        if (logger && logger.error) {
            logger.error('Erro no teste de deletar produto:', error);
        }
    } finally {
        try {
            await disconnectDatabase();
        } catch (disconnectError) {
            console.error('❌ Erro ao desconectar do banco:', disconnectError);
        }
    }
}

// Executa o teste se o script for chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
    testDeleteProductFunctionality();
}

export default testDeleteProductFunctionality;
