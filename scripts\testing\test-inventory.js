#!/usr/bin/env node

/**
 * Script para testar todas as funções de inventário implementadas
 * Este script executa testes manuais das funcionalidades de estoque
 */

import mongoose from 'mongoose';
import Product from '../src/models/Product.js';
import Store from '../src/models/Store.js';
import StockItem from '../src/models/StockItem.js';
import { logger } from '../src/utils/logger.js';

// Configuração do ambiente de teste
const TEST_CONFIG = {
    MONGODB_URI: process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/tarolha_test',
    GUILD_ID: 'test-guild-123',
    USER_ID: 'test-user-456'
};

/**
 * Conecta ao banco de dados de teste
 */
async function connectDatabase() {
    try {
        await mongoose.connect(TEST_CONFIG.MONGODB_URI);
        logger.info('✅ Conectado ao banco de dados de teste');
    } catch (error) {
        logger.error('❌ Erro ao conectar ao banco de dados:', error);
        process.exit(1);
    }
}

/**
 * Limpa dados de teste
 */
async function cleanupTestData() {
    try {
        await Store.deleteMany({ guildId: TEST_CONFIG.GUILD_ID });
        await Product.deleteMany({});
        await StockItem.deleteMany({});
        logger.info('🧹 Dados de teste limpos');
    } catch (error) {
        logger.error('❌ Erro ao limpar dados de teste:', error);
    }
}

/**
 * Cria dados de teste
 */
async function createTestData() {
    try {
        // Cria loja de teste
        const testStore = await Store.create({
            name: 'Loja de Teste - Inventário',
            description: 'Loja criada para testar funcionalidades de inventário',
            ownerId: TEST_CONFIG.USER_ID,
            guildId: TEST_CONFIG.GUILD_ID,
            isActive: true
        });

        // Cria produtos de teste
        const products = await Product.create([
            {
                name: 'Produto A - Teste',
                description: 'Produto para testar visualização de estoque',
                price: 15.99,
                category: 'Teste',
                storeId: testStore._id,
                status: 'active',
                emoji: '🧪'
            },
            {
                name: 'Produto B - Teste',
                description: 'Produto para testar edição',
                price: 25.50,
                category: 'Teste',
                storeId: testStore._id,
                status: 'active',
                emoji: '⚗️'
            }
        ]);

        // Cria itens de estoque variados
        const stockItems = [];
        
        // Para o Produto A - muitos itens para testar paginação
        for (let i = 1; i <= 25; i++) {
            stockItems.push({
                productId: products[0]._id,
                content: `Item disponível A${i} - Conteúdo de teste para busca`,
                status: 'available'
            });
        }

        // Adiciona alguns itens vendidos, reservados e expirados
        stockItems.push(
            {
                productId: products[0]._id,
                content: 'Item vendido A1 - Teste de busca vendido',
                status: 'sold',
                soldAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 dia atrás
            },
            {
                productId: products[0]._id,
                content: 'Item reservado A1 - Teste de busca reservado',
                status: 'reserved',
                reservedAt: new Date()
            },
            {
                productId: products[0]._id,
                content: 'Item expirado A1 - Teste de busca expirado',
                status: 'expired'
            }
        );

        // Para o Produto B - poucos itens
        for (let i = 1; i <= 3; i++) {
            stockItems.push({
                productId: products[1]._id,
                content: `Item disponível B${i} - Produto B teste`,
                status: 'available'
            });
        }

        await StockItem.create(stockItems);

        logger.info('✅ Dados de teste criados:');
        logger.info(`   📦 Loja: ${testStore.name} (ID: ${testStore._id})`);
        logger.info(`   🛍️ Produtos: ${products.length}`);
        logger.info(`   📋 Itens de estoque: ${stockItems.length}`);

        return { testStore, products };

    } catch (error) {
        logger.error('❌ Erro ao criar dados de teste:', error);
        throw error;
    }
}

/**
 * Testa funcionalidades de resumo de estoque
 */
async function testStockSummary(productId) {
    try {
        logger.info('\n🧮 Testando resumo de estoque...');
        
        const summary = await StockItem.getStockSummary(productId);
        
        logger.info('📊 Resumo do estoque:');
        logger.info(`   🟢 Disponível: ${summary.available}`);
        logger.info(`   🔴 Vendido: ${summary.sold}`);
        logger.info(`   🟡 Reservado: ${summary.reserved}`);
        logger.info(`   ⚫ Expirado: ${summary.expired}`);
        logger.info(`   📈 Total: ${summary.total}`);

        // Testa contagem por produto
        const count = await StockItem.countByProduct(productId);
        logger.info(`   🔢 Contagem direta: ${count}`);

        // Testa busca de itens disponíveis
        const availableItems = await StockItem.findAvailableByProduct(productId);
        logger.info(`   ✅ Itens disponíveis encontrados: ${availableItems.length}`);

        return summary;

    } catch (error) {
        logger.error('❌ Erro ao testar resumo de estoque:', error);
        throw error;
    }
}

/**
 * Testa funcionalidades de busca
 */
async function testStockSearch(productId) {
    try {
        logger.info('\n🔍 Testando busca no estoque...');

        // Teste 1: Busca por termo específico
        const searchTerm1 = 'disponível';
        const results1 = await StockItem.find({
            productId: productId,
            content: { $regex: searchTerm1, $options: 'i' }
        });
        logger.info(`   🔎 Busca por "${searchTerm1}": ${results1.length} resultados`);

        // Teste 2: Busca com filtro de status
        const searchTerm2 = 'teste';
        const results2 = await StockItem.find({
            productId: productId,
            content: { $regex: searchTerm2, $options: 'i' },
            status: 'available'
        });
        logger.info(`   🔎 Busca por "${searchTerm2}" (disponível): ${results2.length} resultados`);

        // Teste 3: Busca que não retorna resultados
        const searchTerm3 = 'inexistente';
        const results3 = await StockItem.find({
            productId: productId,
            content: { $regex: searchTerm3, $options: 'i' }
        });
        logger.info(`   🔎 Busca por "${searchTerm3}": ${results3.length} resultados`);

        return { results1, results2, results3 };

    } catch (error) {
        logger.error('❌ Erro ao testar busca no estoque:', error);
        throw error;
    }
}

/**
 * Testa paginação
 */
async function testPagination(productId) {
    try {
        logger.info('\n📄 Testando paginação...');

        const availableItems = await StockItem.find({ 
            productId: productId, 
            status: 'available' 
        }).sort({ createdAt: -1 });

        const itemsPerPage = 10;
        const totalPages = Math.ceil(availableItems.length / itemsPerPage);

        logger.info(`   📋 Total de itens disponíveis: ${availableItems.length}`);
        logger.info(`   📄 Total de páginas (10 por página): ${totalPages}`);

        // Testa primeira página
        const page1Items = availableItems.slice(0, itemsPerPage);
        logger.info(`   📄 Página 1: ${page1Items.length} itens`);

        // Testa segunda página (se existir)
        if (totalPages > 1) {
            const page2Items = availableItems.slice(itemsPerPage, itemsPerPage * 2);
            logger.info(`   📄 Página 2: ${page2Items.length} itens`);
        }

        return { totalPages, itemsPerPage };

    } catch (error) {
        logger.error('❌ Erro ao testar paginação:', error);
        throw error;
    }
}

/**
 * Executa todos os testes
 */
async function runAllTests() {
    try {
        logger.info('🚀 Iniciando testes de inventário...\n');

        // Conecta ao banco
        await connectDatabase();

        // Limpa dados antigos
        await cleanupTestData();

        // Cria dados de teste
        const { testStore, products } = await createTestData();

        // Executa testes para o primeiro produto (que tem mais itens)
        const productId = products[0]._id;
        
        await testStockSummary(productId);
        await testStockSearch(productId);
        await testPagination(productId);

        logger.info('\n✅ Todos os testes executados com sucesso!');
        logger.info('\n📋 Resumo dos testes:');
        logger.info('   ✅ Resumo de estoque - OK');
        logger.info('   ✅ Busca no estoque - OK');
        logger.info('   ✅ Paginação - OK');
        logger.info('\n🎯 Funcionalidades testadas:');
        logger.info('   • Visualização de estoque com paginação');
        logger.info('   • Busca por conteúdo com filtros de status');
        logger.info('   • Cálculo de resumos de estoque');
        logger.info('   • Contagem e agregação de dados');
        logger.info('   • Edição de produtos (estrutura)');

    } catch (error) {
        logger.error('❌ Erro durante os testes:', error);
    } finally {
        // Limpa dados de teste
        await cleanupTestData();
        
        // Desconecta do banco
        await mongoose.disconnect();
        logger.info('🔌 Desconectado do banco de dados');
    }
}

// Executa os testes se o script for chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests().catch(console.error);
}

export { runAllTests, testStockSummary, testStockSearch, testPagination };
