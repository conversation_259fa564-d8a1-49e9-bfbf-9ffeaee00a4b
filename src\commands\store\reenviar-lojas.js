import { SlashCommandBuilder } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import { StoreManager } from '../../utils/managers/storeManager.js';
import Store from '../../models/Store.js';

export default {
    data: new SlashCommandBuilder()
        .setName('reenviar-lojas')
        .setDescription('Reenvia mensagens das lojas (apaga e envia atualizadas)')
        .addStringOption(option =>
            option.setName('loja')
                .setDescription('Nome da loja específica para reenviar (deixe vazio para todas)')
                .setRequired(false)
                .setAutocomplete(true)
        ),
    
    async execute(interaction) {
        try {
            // Verificação se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem executar este comando.',
                    ephemeral: true
                });
            }

            // Defer a resposta pois pode demorar
            await interaction.deferReply({ ephemeral: true });

            const lojaEspecifica = interaction.options.getString('loja');
            const guildId = interaction.guild.id;

            let stores;
            if (lojaEspecifica) {
                // Busca loja específica
                stores = await Store.find({ 
                    guildId, 
                    name: { $regex: new RegExp(`^${lojaEspecifica}$`, 'i') },
                    isActive: true 
                });
                
                if (stores.length === 0) {
                    return await interaction.editReply({
                        content: `❌ Loja "${lojaEspecifica}" não encontrada.`
                    });
                }
            } else {
                // Busca todas as lojas do servidor
                stores = await Store.findByGuild(guildId);
                
                if (stores.length === 0) {
                    return await interaction.editReply({
                        content: '❌ Não há lojas criadas neste servidor.'
                    });
                }
            }

            let sucessos = 0;
            let erros = 0;
            const resultados = [];

            for (const store of stores) {
                try {
                    const resultado = await StoreManager.resendStoreMessage(interaction.guild, store);
                    if (resultado.success) {
                        sucessos++;
                        resultados.push(`✅ ${store.name}`);
                        logger.info(`Loja "${store.name}" reenviada com sucesso por ${interaction.user.tag}`);
                    } else {
                        erros++;
                        resultados.push(`❌ ${store.name}: ${resultado.error}`);
                    }
                } catch (error) {
                    erros++;
                    resultados.push(`❌ ${store.name}: ${error.message}`);
                    logger.error(`Erro ao reenviar loja "${store.name}":`, error);
                }
            }

            // Monta a resposta
            let resposta = '';
            if (lojaEspecifica) {
                resposta = `🔄 **Reenvio da loja "${lojaEspecifica}" concluído**\n\n`;
            } else {
                resposta = `🔄 **Reenvio de ${stores.length} loja(s) concluído**\n\n`;
            }

            resposta += `✅ Sucessos: ${sucessos}\n`;
            resposta += `❌ Erros: ${erros}\n\n`;
            
            if (resultados.length > 0) {
                resposta += '**Detalhes:**\n' + resultados.join('\n');
            }

            await interaction.editReply({
                content: resposta
            });

        } catch (error) {
            logger.error('Erro ao executar comando reenviar-lojas:', error);
            
            const errorMessage = {
                content: '❌ Erro ao reenviar lojas.'
            };

            if (interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply({ ...errorMessage, ephemeral: true });
            }
        }
    },

    async autocomplete(interaction) {
        try {
            const focusedValue = interaction.options.getFocused();
            const guildId = interaction.guild.id;

            // Busca lojas ativas no servidor
            const stores = await Store.findByGuild(guildId);
            
            // Filtra lojas baseado no que o usuário digitou
            const filtered = stores
                .filter(store => store.name.toLowerCase().includes(focusedValue.toLowerCase()))
                .slice(0, 25) // Discord limita a 25 opções
                .map(store => ({
                    name: store.name,
                    value: store.name
                }));

            await interaction.respond(filtered);
        } catch (error) {
            logger.error('Erro no autocomplete de reenviar-lojas:', error);
            await interaction.respond([]);
        }
    }
};