import mongoose from 'mongoose';
import dotenv from 'dotenv';
dotenv.config();

async function auditDatabase() {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('=== DATABASE AUDIT REPORT ===\n');
        
        const db = mongoose.connection.db;
        const collections = await db.listCollections().toArray();
        
        console.log(`Total collections found: ${collections.length}\n`);
        
        const auditResults = [];
        
        for (const collection of collections) {
            const collectionName = collection.name;
            const count = await db.collection(collectionName).countDocuments();
            const indexes = await db.collection(collectionName).indexes();
            const sampleDoc = await db.collection(collectionName).findOne();
            
            const result = {
                name: collectionName,
                documentCount: count,
                indexCount: indexes.length,
                sampleStructure: sampleDoc ? Object.keys(sampleDoc) : [],
                indexes: indexes.map(idx => ({
                    name: idx.name,
                    keys: idx.key,
                    unique: idx.unique || false,
                    sparse: idx.sparse || false
                }))
            };
            
            auditResults.push(result);
            
            console.log(`📁 Collection: ${collectionName}`);
            console.log(`   Documents: ${count}`);
            console.log(`   Indexes: ${indexes.length}`);
            console.log(`   Sample structure: ${sampleDoc ? Object.keys(sampleDoc).join(', ') : 'No documents'}`);
            console.log(`   Index details: ${indexes.map(idx => idx.name).join(', ')}\n`);
        }
        
        // Identify potentially unused collections
        console.log('=== ANALYSIS ===\n');
        
        const knownModels = [
            'products', 'stores', 'orders', 'users', 'stock_items', 
            'botconfigs', 'emojiconfigs'
        ];
        
        const activeCollections = auditResults.filter(col => 
            knownModels.includes(col.name.toLowerCase()) || col.documentCount > 0
        );
        
        const potentiallyUnused = auditResults.filter(col => 
            !knownModels.includes(col.name.toLowerCase()) && col.documentCount === 0
        );
        
        console.log(`Active collections (${activeCollections.length}):`);
        activeCollections.forEach(col => {
            console.log(`  - ${col.name} (${col.documentCount} docs, ${col.indexCount} indexes)`);
        });
        
        if (potentiallyUnused.length > 0) {
            console.log(`\nPotentially unused collections (${potentiallyUnused.length}):`);
            potentiallyUnused.forEach(col => {
                console.log(`  - ${col.name} (${col.documentCount} docs, ${col.indexCount} indexes)`);
            });
        }
        
        // Check for orphaned indexes
        console.log('\n=== INDEX ANALYSIS ===\n');
        
        for (const collection of auditResults) {
            if (collection.indexes.length > 1) { // More than just _id
                console.log(`${collection.name} indexes:`);
                collection.indexes.forEach(idx => {
                    if (idx.name !== '_id_') {
                        console.log(`  - ${idx.name}: ${JSON.stringify(idx.keys)} ${idx.unique ? '(unique)' : ''} ${idx.sparse ? '(sparse)' : ''}`);
                    }
                });
                console.log('');
            }
        }
        
        await mongoose.disconnect();
        console.log('Database audit completed successfully.');
        
    } catch (error) {
        console.error('Database audit failed:', error);
        process.exit(1);
    }
}

auditDatabase();