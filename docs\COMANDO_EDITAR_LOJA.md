# 🏪 Comando `/editar-loja`

Este documento explica como usar o comando `/editar-loja` para modificar lojas existentes no servidor.

## 📋 Funcionalidades

### Comando `/editar-loja`
- **Permissão**: Apenas administradores podem executar
- **Função**: Permite editar lojas já criadas no servidor
- **Resultado**: Atualiza os dados da loja e o embed no canal correspondente

## 🚀 Como Usar

### 1. Executar o Comando
```
/editar-loja
```

### 2. Selecionar a Loja
- O bot exibirá um dropdown com todas as lojas ativas do servidor
- Cada opção mostra:
  - Nome da loja
  - Canal onde está localizada
  - Data de criação
- Selecione a loja que deseja editar

### 3. Preencher o Formulário de Edição
O modal de edição contém os mesmos campos da criação, mas com algumas diferenças importantes:

#### **Campos Disponíveis:**

**🖼️ Banner da Loja**
- Campo pré-preenchido com o valor atual
- Deixe em branco para manter o banner atual
- Se preenchido, deve ser uma URL válida de imagem

**📝 Nome da Loja**
- Campo pré-preenchido com o nome atual
- Deixe em branco para manter o nome atual
- Se alterado, não pode conflitar com outras lojas do servidor

**🎨 Cor da Loja**
- Campo pré-preenchido com a cor atual
- Deixe em branco para manter a cor atual
- Aceita códigos hex (#FFFFFF) ou nomes de cores (red, blue, etc)

**📄 Descrição da Loja**
- Campo pré-preenchido com a descrição atual
- Deixe em branco para manter a descrição atual
- Máximo de 1000 caracteres

### 4. Confirmar as Alterações
- Apenas campos preenchidos serão atualizados
- Campos em branco mantêm os valores originais
- O sistema validará apenas os campos que foram modificados
- Após a confirmação, o embed da loja será atualizado automaticamente

## ✅ Validações

### Validações Aplicadas:
- **Banner**: Deve ser uma URL válida de imagem (jpg, png, gif, webp)
- **Nome**: Não pode ser duplicado no mesmo servidor
- **Cor**: Deve ser um código hex válido ou nome de cor reconhecido
- **Permissões**: Apenas administradores podem editar lojas

### Cores Suportadas:
- **Códigos Hex**: `#FF0000`, `#00FF00`, `#0000FF`, etc.
- **Nomes de Cores**: `red`, `green`, `blue`, `yellow`, `purple`, `orange`, `pink`, `black`, `white`, `gray`/`grey`

## 🔧 Funcionalidades Técnicas

### Atualização Inteligente:
- Apenas campos modificados são atualizados no banco de dados
- O embed da loja é automaticamente atualizado no canal
- Histórico de modificações é mantido (quem editou e quando)

### Segurança:
- Verificação de permissões de administrador
- Validação de propriedade da loja (deve pertencer ao servidor atual)
- Verificação de existência e status ativo da loja

### Logs:
- Todas as edições são registradas nos logs do sistema
- Inclui informações sobre quais campos foram alterados
- Rastreamento de usuário e servidor

## ❌ Possíveis Erros

### Erros Comuns:
- **"Apenas administradores podem executar este comando"**: Usuário sem permissão
- **"Não há lojas criadas neste servidor"**: Nenhuma loja ativa encontrada
- **"Loja não encontrada ou inativa"**: Loja foi deletada ou desativada
- **"Já existe uma loja com este nome no servidor"**: Nome duplicado
- **"URL do banner inválida"**: Banner não é uma URL válida de imagem
- **"Cor inválida"**: Formato de cor não reconhecido
- **"Nenhuma alteração foi detectada"**: Todos os campos estão iguais aos valores atuais

## 📝 Exemplo de Uso

1. Execute `/editar-loja`
2. Selecione "Loja de Eletrônicos" no dropdown
3. No modal que abrir:
   - **Banner**: Deixe em branco (manter atual)
   - **Nome**: `Loja de Eletrônicos Premium`
   - **Cor**: `#FF6B35`
   - **Descrição**: Deixe em branco (manter atual)
4. Clique em "Enviar"
5. O sistema atualizará apenas o nome e a cor da loja

## 🔄 Integração com Sistema Existente

O comando `/editar-loja` integra perfeitamente com:
- Sistema de criação de lojas (`/criar-loja`)
- Sistema de produtos existente
- Permissões e validações do servidor
- Logs e auditoria do sistema

---

**💡 Dica**: Use este comando para manter suas lojas sempre atualizadas sem precisar recriá-las do zero!
