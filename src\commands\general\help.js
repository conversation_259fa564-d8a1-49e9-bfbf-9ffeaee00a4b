import { SlashCommandBuilder, EmbedBuilder } from 'discord.js';
import { COLORS } from '../../config/constants.js';
import { getEmojiFromInteraction } from '../../utils/ui/emojiHelper.js';

export default {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Mostra todos os comandos disponíveis do bot'),

    async execute(interaction) {
        // Obtém emojis personalizados para este servidor
        const infoEmoji = await getEmojiFromInteraction(interaction, 'INFO');

        const embed = new EmbedBuilder()
            .setColor(COLORS.PRIMARY || '#0099ff')
            .setTitle(`${infoEmoji} Comandos Disponíveis`)
            .setDescription('Lista de todos os comandos do bot organizados por categoria')
            .setThumbnail(interaction.client.user.displayAvatarURL())
            .addFields(
                {
                    name: '🔧 Comandos Gerais',
                    value: [
                        '`/ping` - Verifica a latência do bot',
                        '`/info` - Mostra informações sobre o bot',
                        '`/help` - Mostra esta lista de comandos',
                        '`/configbot` - Configura opções do bot (apenas admins)',
                        '`/emojis` - Configura emojis do bot (apenas admins)'
                    ].join('\n'),
                    inline: false
                },
                {
                    name: '🏪 Comandos da Loja',
                    value: [
                        '`/criar-loja` - Cria uma nova loja (apenas admins)',
                        '`/deletar-loja` - Remove uma loja existente (apenas admins)',
                        '`/editar-loja` - Edita informações de uma loja (apenas admins)',
                        '`/reenviar-lojas` - Reenvia mensagens das lojas (apenas admins)'
                    ].join('\n'),
                    inline: false
                },
                {
                    name: '📦 Comandos de Produtos',
                    value: [
                        '`/criar-produto` - Adiciona um produto à loja (apenas admins)',
                        '`/editar-produtos` - Edita produtos existentes (apenas admins)',
                        '`/deletar-produto` - Remove um produto da loja (apenas admins)'
                    ].join('\n'),
                    inline: false
                },
                {
                    name: '📋 Comandos de Estoque',
                    value: [
                        '`/criar-estoque` - Adiciona itens ao estoque (apenas admins)',
                        '`/visualizar-estoque` - Visualiza estoque de produtos (apenas admins)',
                        '`/editar-estoque` - Edita itens do estoque (apenas admins)',
                        '`/deletar-estoque` - Remove itens do estoque (apenas admins)'
                    ].join('\n'),
                    inline: false
                }
            )
            .setFooter({ 
                text: `Solicitado por ${interaction.user.username} • Use /configbot para configurar o bot`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();

        await interaction.reply({ embeds: [embed] });
    }
};