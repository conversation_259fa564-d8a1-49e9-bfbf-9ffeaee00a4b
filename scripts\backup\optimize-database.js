import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Comprehensive Database Optimization Script
 * 
 * This script performs a complete database optimization including:
 * 1. Performance analysis and benchmarking
 * 2. Index optimization
 * 3. Data cleanup and migration
 * 4. Cache warming
 * 5. Performance validation
 */

class DatabaseOptimizer {
    constructor() {
        this.db = null;
        this.dryRun = process.argv.includes('--dry-run');
        this.verbose = process.argv.includes('--verbose');
        this.skipBackup = process.argv.includes('--skip-backup');
        this.optimizationResults = {
            startTime: Date.now(),
            phases: {},
            metrics: {
                before: {},
                after: {}
            }
        };
    }

    async connect() {
        try {
            await mongoose.connect(process.env.MONGODB_URI);
            this.db = mongoose.connection.db;
            console.log('✅ Connected to MongoDB');
        } catch (error) {
            console.error('❌ Failed to connect to MongoDB:', error.message);
            throw error;
        }
    }

    async disconnect() {
        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');
    }

    log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] [${level}] ${message}`);
    }

    /**
     * Phase 1: Analyze current database performance
     */
    async analyzePerformance() {
        this.log('=== PHASE 1: PERFORMANCE ANALYSIS ===');
        const phaseStart = Date.now();

        try {
            // Get database stats
            const dbStats = await this.db.stats();
            
            // Analyze collections
            const collections = await this.db.listCollections().toArray();
            const collectionStats = {};
            
            for (const collection of collections) {
                const collName = collection.name;
                try {
                    const count = await this.db.collection(collName).countDocuments();
                    const indexes = await this.db.collection(collName).indexes();
                    
                    // Get collection stats using aggregation
                    const sizeStats = await this.db.collection(collName).aggregate([
                        { $group: { _id: null, avgSize: { $avg: { $bsonSize: '$$ROOT' } } } }
                    ]).toArray();
                    
                    const avgObjSize = sizeStats.length > 0 ? Math.round(sizeStats[0].avgSize) : 0;
                    const estimatedStorageSize = count * avgObjSize;
                    
                    collectionStats[collName] = {
                        documents: count,
                        avgObjSize,
                        storageSize: Math.round(estimatedStorageSize / 1024 / 1024), // MB
                        indexCount: indexes.length,
                        indexes: indexes.map(idx => ({
                            name: idx.name,
                            keys: idx.key,
                            size: 0 // Size not available in this method
                        }))
                    };
                    
                    if (this.verbose) {
                        this.log(`${collName}: ${count} docs, ${indexes.length} indexes, ~${Math.round(estimatedStorageSize / 1024 / 1024)}MB`);
                    }
                } catch (error) {
                    this.log(`Failed to analyze collection ${collName}: ${error.message}`, 'ERROR');
                    collectionStats[collName] = {
                        documents: 0,
                        avgObjSize: 0,
                        storageSize: 0,
                        indexCount: 0,
                        indexes: []
                    };
                }
            }

            this.optimizationResults.metrics.before = {
                totalSize: Math.round(dbStats.dataSize / 1024 / 1024), // MB
                totalIndexSize: Math.round(dbStats.indexSize / 1024 / 1024), // MB
                totalCollections: collections.length,
                collections: collectionStats
            };

            this.log(`Database size: ${this.optimizationResults.metrics.before.totalSize}MB`);
            this.log(`Index size: ${this.optimizationResults.metrics.before.totalIndexSize}MB`);
            this.log(`Collections: ${this.optimizationResults.metrics.before.totalCollections}`);

        } catch (error) {
            this.log(`Performance analysis failed: ${error.message}`, 'ERROR');
            throw error;
        }

        this.optimizationResults.phases.analysis = {
            duration: Date.now() - phaseStart,
            status: 'completed'
        };
    }

    /**
     * Phase 2: Optimize indexes
     */
    async optimizeIndexes() {
        this.log('=== PHASE 2: INDEX OPTIMIZATION ===');
        const phaseStart = Date.now();
        const indexChanges = {
            dropped: [],
            created: [],
            errors: []
        };

        try {
            const indexOptimizations = [
                {
                    collection: 'products',
                    actions: [
                        {
                            type: 'drop',
                            indexes: ['isActive_1', 'createdAt_1', 'price_1']
                        },
                        {
                            type: 'create',
                            indexes: [
                                {
                                    keys: { storeId: 1, isActive: 1, featured: -1, createdAt: -1 },
                                    options: { name: 'store_active_featured_date' }
                                },
                                {
                                    keys: { category: 1, price: 1, isActive: 1 },
                                    options: { name: 'category_price_active' }
                                },
                                {
                                    keys: { isActive: 1, totalSold: -1 },
                                    options: { name: 'active_bestsellers' }
                                }
                            ]
                        }
                    ]
                },
                {
                    collection: 'orders',
                    actions: [
                        {
                            type: 'drop',
                            indexes: ['userId_1', 'productId_1', 'status_1', 'createdAt_1', 'guildId_1']
                        },
                        {
                            type: 'create',
                            indexes: [
                                {
                                    keys: { guildId: 1, status: 1, createdAt: -1 },
                                    options: { name: 'guild_status_date_optimized' }
                                },
                                {
                                    keys: { userId: 1, status: 1, createdAt: -1 },
                                    options: { name: 'user_status_date' }
                                },
                                {
                                    keys: { status: 1, paymentMethod: 1 },
                                    options: { name: 'status_payment_method' }
                                }
                            ]
                        }
                    ]
                },
                {
                    collection: 'stock_items',
                    actions: [
                        {
                            type: 'drop',
                            indexes: ['productId_1', 'storeId_1', 'status_1']
                        },
                        {
                            type: 'create',
                            indexes: [
                                {
                                    keys: { productId: 1, status: 1, createdAt: -1 },
                                    options: { name: 'product_status_date_optimized' }
                                },
                                {
                                    keys: { storeId: 1, status: 1 },
                                    options: { name: 'store_status_optimized' }
                                }
                            ]
                        }
                    ]
                },
                {
                    collection: 'users',
                    actions: [
                        {
                            type: 'drop',
                            indexes: ['createdAt_-1', 'vipLevel_1', 'lastActivity_-1']
                        },
                        {
                            type: 'create',
                            indexes: [
                                {
                                    keys: { discordId: 1, isBlacklisted: 1 },
                                    options: { name: 'discord_blacklist_check' }
                                },
                                {
                                    keys: { guildId: 1, vipLevel: -1, totalOrders: -1 },
                                    options: { name: 'guild_vip_orders' }
                                }
                            ]
                        }
                    ]
                }
            ];

            for (const optimization of indexOptimizations) {
                const collectionExists = await this.db.listCollections({ name: optimization.collection }).hasNext();
                if (!collectionExists) {
                    this.log(`Collection ${optimization.collection} does not exist, skipping`);
                    continue;
                }

                const currentIndexes = await this.db.collection(optimization.collection).indexes();
                const currentIndexNames = currentIndexes.map(idx => idx.name);

                for (const action of optimization.actions) {
                    if (action.type === 'drop') {
                        for (const indexName of action.indexes) {
                            if (currentIndexNames.includes(indexName)) {
                                if (this.dryRun) {
                                    this.log(`[DRY RUN] Would drop index: ${optimization.collection}.${indexName}`);
                                } else {
                                    try {
                                        await this.db.collection(optimization.collection).dropIndex(indexName);
                                        indexChanges.dropped.push(`${optimization.collection}.${indexName}`);
                                        this.log(`Dropped index: ${optimization.collection}.${indexName}`);
                                    } catch (error) {
                                        indexChanges.errors.push(`Failed to drop ${optimization.collection}.${indexName}: ${error.message}`);
                                    }
                                }
                            }
                        }
                    } else if (action.type === 'create') {
                        for (const indexData of action.indexes) {
                            if (this.dryRun) {
                                this.log(`[DRY RUN] Would create index: ${optimization.collection}.${indexData.options.name}`);
                            } else {
                                try {
                                    await this.db.collection(optimization.collection).createIndex(indexData.keys, indexData.options);
                                    indexChanges.created.push(`${optimization.collection}.${indexData.options.name}`);
                                    this.log(`Created index: ${optimization.collection}.${indexData.options.name}`);
                                } catch (error) {
                                    if (error.code === 85) {
                                        this.log(`Index already exists: ${optimization.collection}.${indexData.options.name}`);
                                    } else {
                                        indexChanges.errors.push(`Failed to create ${optimization.collection}.${indexData.options.name}: ${error.message}`);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            this.log(`Index optimization completed: ${indexChanges.dropped.length} dropped, ${indexChanges.created.length} created, ${indexChanges.errors.length} errors`);

        } catch (error) {
            this.log(`Index optimization failed: ${error.message}`, 'ERROR');
            throw error;
        }

        this.optimizationResults.phases.indexOptimization = {
            duration: Date.now() - phaseStart,
            status: 'completed',
            changes: indexChanges
        };
    }

    /**
     * Phase 3: Clean up legacy data
     */
    async cleanupLegacyData() {
        this.log('=== PHASE 3: LEGACY DATA CLEANUP ===');
        const phaseStart = Date.now();
        const cleanupResults = {
            collectionsDropped: [],
            documentsRemoved: 0,
            errors: []
        };

        try {
            // Legacy collections to remove
            const legacyCollections = [
                'lojas', 'compras', 'produtos', 'configuracoes',
                'pagamentos', 'estoque'
            ];

            for (const collectionName of legacyCollections) {
                const exists = await this.db.listCollections({ name: collectionName }).hasNext();
                if (!exists) {
                    this.log(`Legacy collection ${collectionName} does not exist`);
                    continue;
                }

                const count = await this.db.collection(collectionName).countDocuments();
                
                if (this.dryRun) {
                    this.log(`[DRY RUN] Would drop legacy collection: ${collectionName} (${count} documents)`);
                    cleanupResults.documentsRemoved += count;
                } else {
                    try {
                        await this.db.collection(collectionName).drop();
                        cleanupResults.collectionsDropped.push(collectionName);
                        cleanupResults.documentsRemoved += count;
                        this.log(`Dropped legacy collection: ${collectionName} (${count} documents)`);
                    } catch (error) {
                        cleanupResults.errors.push(`Failed to drop ${collectionName}: ${error.message}`);
                    }
                }
            }

            // Clean up old analytics data (older than 90 days)
            const analyticsExists = await this.db.listCollections({ name: 'analytics' }).hasNext();
            if (analyticsExists) {
                const cutoffDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
                const oldAnalyticsCount = await this.db.collection('analytics')
                    .countDocuments({ createdAt: { $lt: cutoffDate } });

                if (oldAnalyticsCount > 0) {
                    if (this.dryRun) {
                        this.log(`[DRY RUN] Would remove ${oldAnalyticsCount} old analytics records`);
                    } else {
                        const result = await this.db.collection('analytics')
                            .deleteMany({ createdAt: { $lt: cutoffDate } });
                        this.log(`Removed ${result.deletedCount} old analytics records`);
                        cleanupResults.documentsRemoved += result.deletedCount;
                    }
                }
            }

            this.log(`Legacy cleanup completed: ${cleanupResults.collectionsDropped.length} collections dropped, ${cleanupResults.documentsRemoved} documents removed`);

        } catch (error) {
            this.log(`Legacy cleanup failed: ${error.message}`, 'ERROR');
            throw error;
        }

        this.optimizationResults.phases.legacyCleanup = {
            duration: Date.now() - phaseStart,
            status: 'completed',
            results: cleanupResults
        };
    }

    /**
     * Phase 4: Warm up cache
     */
    async warmUpCache() {
        this.log('=== PHASE 4: CACHE WARM-UP ===');
        const phaseStart = Date.now();
        const warmupResults = {
            itemsCached: 0,
            errors: []
        };

        try {
            // Import cache manager
            const { default: cacheManager } = await import('../src/utils/cacheManager.js');

            // Warm up bot configurations
            const configs = await this.db.collection('bot_configs').find({}).limit(50).toArray();
            for (const config of configs) {
                cacheManager.set(`config:${config.guildId}`, config, 'config');
                warmupResults.itemsCached++;
            }

            // Warm up active stores
            const stores = await this.db.collection('stores').find({ isActive: true }).limit(100).toArray();
            for (const store of stores) {
                cacheManager.set(`store:${store._id}`, store, 'store');
                warmupResults.itemsCached++;
            }

            // Warm up featured products
            const products = await this.db.collection('products')
                .find({ isActive: true, featured: true })
                .limit(200)
                .toArray();
            for (const product of products) {
                cacheManager.set(`product:${product._id}`, product, 'product');
                warmupResults.itemsCached++;
            }

            // Warm up user permissions
            const permissions = await this.db.collection('permissions').find({}).limit(100).toArray();
            for (const permission of permissions) {
                cacheManager.set(`permission:${permission.userId}:${permission.guildId}`, permission, 'permission');
                warmupResults.itemsCached++;
            }

            this.log(`Cache warm-up completed: ${warmupResults.itemsCached} items cached`);

        } catch (error) {
            this.log(`Cache warm-up failed: ${error.message}`, 'ERROR');
            warmupResults.errors.push(error.message);
        }

        this.optimizationResults.phases.cacheWarmup = {
            duration: Date.now() - phaseStart,
            status: 'completed',
            results: warmupResults
        };
    }

    /**
     * Phase 5: Validate optimization results
     */
    async validateOptimization() {
        this.log('=== PHASE 5: VALIDATION ===');
        const phaseStart = Date.now();

        try {
            // Re-analyze performance
            const dbStats = await this.db.stats();
            const collections = await this.db.listCollections().toArray();
            const collectionStats = {};
            
            for (const collection of collections) {
                const collName = collection.name;
                try {
                    const count = await this.db.collection(collName).countDocuments();
                    const indexes = await this.db.collection(collName).indexes();
                    
                    // Get collection stats using aggregation
                    const sizeStats = await this.db.collection(collName).aggregate([
                        { $group: { _id: null, avgSize: { $avg: { $bsonSize: '$$ROOT' } } } }
                    ]).toArray();
                    
                    const avgObjSize = sizeStats.length > 0 ? Math.round(sizeStats[0].avgSize) : 0;
                    const estimatedStorageSize = count * avgObjSize;
                    
                    collectionStats[collName] = {
                        documents: count,
                        avgObjSize,
                        storageSize: Math.round(estimatedStorageSize / 1024 / 1024), // MB
                        indexCount: indexes.length
                    };
                } catch (error) {
                    this.log(`Failed to analyze collection ${collName}: ${error.message}`, 'ERROR');
                    collectionStats[collName] = {
                        documents: 0,
                        avgObjSize: 0,
                        storageSize: 0,
                        indexCount: 0
                    };
                }
            }

            this.optimizationResults.metrics.after = {
                totalSize: Math.round(dbStats.dataSize / 1024 / 1024), // MB
                totalIndexSize: Math.round(dbStats.indexSize / 1024 / 1024), // MB
                totalCollections: collections.length,
                collections: collectionStats
            };

            // Calculate improvements
            const sizeDiff = this.optimizationResults.metrics.before.totalSize - this.optimizationResults.metrics.after.totalSize;
            const indexSizeDiff = this.optimizationResults.metrics.before.totalIndexSize - this.optimizationResults.metrics.after.totalIndexSize;
            const collectionDiff = this.optimizationResults.metrics.before.totalCollections - this.optimizationResults.metrics.after.totalCollections;

            this.log(`Database size change: ${sizeDiff > 0 ? '-' : '+'}${Math.abs(sizeDiff)}MB`);
            this.log(`Index size change: ${indexSizeDiff > 0 ? '-' : '+'}${Math.abs(indexSizeDiff)}MB`);
            this.log(`Collections removed: ${collectionDiff}`);

        } catch (error) {
            this.log(`Validation failed: ${error.message}`, 'ERROR');
            throw error;
        }

        this.optimizationResults.phases.validation = {
            duration: Date.now() - phaseStart,
            status: 'completed'
        };
    }

    /**
     * Generate optimization report
     */
    async generateReport() {
        this.log('=== GENERATING OPTIMIZATION REPORT ===');

        const totalDuration = Date.now() - this.optimizationResults.startTime;
        const report = {
            ...this.optimizationResults,
            totalDuration,
            completedAt: new Date().toISOString(),
            dryRun: this.dryRun,
            summary: {
                sizeSaved: this.optimizationResults.metrics.before.totalSize - this.optimizationResults.metrics.after.totalSize,
                indexSizeSaved: this.optimizationResults.metrics.before.totalIndexSize - this.optimizationResults.metrics.after.totalIndexSize,
                collectionsRemoved: this.optimizationResults.metrics.before.totalCollections - this.optimizationResults.metrics.after.totalCollections,
                totalPhases: Object.keys(this.optimizationResults.phases).length,
                successfulPhases: Object.values(this.optimizationResults.phases).filter(p => p.status === 'completed').length
            }
        };

        // Save report
        const reportPath = join(__dirname, '..', 'logs', `optimization-report-${Date.now()}.json`);
        try {
            await fs.mkdir(join(__dirname, '..', 'logs'), { recursive: true });
            await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
            this.log(`Optimization report saved: ${reportPath}`);
        } catch (error) {
            this.log(`Failed to save report: ${error.message}`, 'ERROR');
        }

        return report;
    }

    /**
     * Run complete optimization process
     */
    async run() {
        try {
            await this.connect();

            this.log('🚀 Starting database optimization...');
            if (this.dryRun) {
                this.log('Running in DRY RUN mode - no changes will be made');
            }

            // Run optimization phases
            await this.analyzePerformance();
            await this.optimizeIndexes();
            await this.cleanupLegacyData();
            await this.warmUpCache();
            await this.validateOptimization();

            // Generate final report
            const report = await this.generateReport();

            this.log('✅ Database optimization completed successfully!');
            this.log(`Total time: ${Math.round(report.totalDuration / 1000)}s`);
            this.log(`Size saved: ${report.summary.sizeSaved}MB`);
            this.log(`Collections removed: ${report.summary.collectionsRemoved}`);

            return report;

        } catch (error) {
            this.log(`❌ Database optimization failed: ${error.message}`, 'ERROR');
            throw error;
        } finally {
            await this.disconnect();
        }
    }
}

// Run the optimization
const optimizer = new DatabaseOptimizer();
optimizer.run().catch(console.error);