import dotenv from 'dotenv';
dotenv.config();

import mongoose from 'mongoose';

// Simple test to verify the delete product fix
async function testDeleteProductQueries() {
    try {
        console.log('🔍 Testing delete product queries...\n');

        // Connect to MongoDB
        const mongoUri = process.env.MONGODB_URI;
        if (!mongoUri) {
            throw new Error('MONGODB_URI not found in environment variables');
        }

        await mongoose.connect(mongoUri);
        console.log('✅ Connected to MongoDB\n');

        // Define schemas inline for testing
        const storeSchema = new mongoose.Schema({
            name: String,
            guildId: String,
            isActive: { type: Boolean, default: true }
        }, { collection: 'stores' });

        const productSchema = new mongoose.Schema({
            name: String,
            storeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Store' },
            status: { 
                type: String, 
                enum: ['active', 'inactive', 'out_of_stock', 'discontinued'],
                default: 'active'
            }
        }, { collection: 'products' });

        const stockItemSchema = new mongoose.Schema({
            productId: { type: mongoose.Schema.Types.ObjectId, ref: 'Product' },
            status: { type: String, default: 'available' }
        }, { collection: 'stock_items' });

        const Store = mongoose.model('Store', storeSchema);
        const Product = mongoose.model('Product', productSchema);
        const StockItem = mongoose.model('StockItem', stockItemSchema);

        // Test 1: Find active stores
        console.log('📋 Test 1: Finding active stores...');
        const stores = await Store.find({ isActive: true }).limit(3);
        console.log(`   Found ${stores.length} active stores`);
        
        if (stores.length === 0) {
            console.log('❌ No active stores found. Cannot continue tests.');
            return;
        }

        // Test 2: Find products using the NEW query (fixed)
        console.log('\n📦 Test 2: Finding products with NEW query (status != discontinued)...');
        for (const store of stores) {
            const products = await Product.find({ 
                storeId: store._id, 
                status: { $ne: 'discontinued' }
            }).sort({ name: 1 });

            console.log(`   Store "${store.name}": ${products.length} products found`);
            
            if (products.length > 0) {
                console.log('   Products:');
                products.slice(0, 3).forEach(product => {
                    console.log(`      - ${product.name} (status: ${product.status})`);
                });
            }
        }

        // Test 3: Compare with OLD query (would have failed)
        console.log('\n🔍 Test 3: Testing what OLD query would find (isActive: true)...');
        for (const store of stores) {
            // This simulates the old broken query
            const oldQueryResults = await Product.find({ 
                storeId: store._id, 
                isActive: true  // This field doesn't exist in schema!
            }).sort({ name: 1 });

            console.log(`   Store "${store.name}": ${oldQueryResults.length} products with OLD query`);
        }

        // Test 4: Find a specific product for deletion test
        console.log('\n🎯 Test 4: Testing individual product lookup...');
        const allProducts = await Product.find({ 
            status: { $ne: 'discontinued' } 
        }).limit(1);

        if (allProducts.length > 0) {
            const testProduct = allProducts[0];
            console.log(`   Testing product: ${testProduct.name}`);

            // Test the fixed query
            const foundProduct = await Product.findOne({ 
                _id: testProduct._id, 
                status: { $ne: 'discontinued' } 
            }).populate('storeId');

            if (foundProduct) {
                console.log('   ✅ Product found with NEW query');
                console.log(`      - Name: ${foundProduct.name}`);
                console.log(`      - Status: ${foundProduct.status}`);
                
                // Count stock items
                const stockCount = await StockItem.countDocuments({ 
                    productId: foundProduct._id, 
                    status: 'available' 
                });
                console.log(`      - Available stock: ${stockCount}`);
            } else {
                console.log('   ❌ Product NOT found with NEW query');
            }

            // Test what the old query would find
            const oldFoundProduct = await Product.findOne({ 
                _id: testProduct._id, 
                isActive: true  // This would fail
            });

            console.log(`   Old query result: ${oldFoundProduct ? 'Found' : 'NOT FOUND (expected)'}`);
        }

        // Test 5: Status analysis
        console.log('\n📊 Test 5: Product status analysis...');
        const statusCounts = await Product.aggregate([
            { $group: { _id: '$status', count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]);

        console.log('   Status distribution:');
        statusCounts.forEach(item => {
            console.log(`      - ${item._id || 'undefined'}: ${item.count} products`);
        });

        console.log('\n✅ All tests completed successfully!');
        console.log('\n📋 Summary:');
        console.log('   - NEW query (status != discontinued) works correctly');
        console.log('   - OLD query (isActive: true) fails as expected');
        console.log('   - Product lookup and stock counting work properly');
        console.log('   - Delete product functionality should now work!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from MongoDB');
    }
}

testDeleteProductQueries();
