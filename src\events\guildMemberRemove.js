import { Events } from 'discord.js';
import { logger } from '../utils/logging/logger.js';

export default {
    name: Events.GuildMemberRemove,
    async execute(member) {
        try {
            await logger.event(`Memb<PERSON> saiu do servidor`, {
                guildId: member.guild.id,
                userId: member.user.id
            }, {
                user: member.user.tag,
                guild: member.guild.name,
                memberCount: member.guild.memberCount,
                joinedAt: member.joinedAt?.toISOString() || 'Desconhecido',
                roles: member.roles.cache.size - 1, // -1 para remover @everyone
                isBot: member.user.bot
            });

        } catch (error) {
            await logger.logStructured('ERROR', 'EVENT', 'Erro no evento guildMemberRemove', {
                guildId: member.guild?.id,
                userId: member.user?.id
            }, {
                error: error.message,
                stack: error.stack
            });
        }
    }
};
