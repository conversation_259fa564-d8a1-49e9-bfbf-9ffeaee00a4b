import { readdirSync, statSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath, pathToFileURL } from 'url';
import { logger } from './logging/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Carrega recursivamente todos os eventos da pasta events e suas subpastas
 * @param {string} eventsPath - Caminho da pasta de eventos
 * @param {Client} client - Instância do cliente Discord
 * @param {number} eventCount - Contador de eventos carregados
 * @returns {number} Número total de eventos carregados
 */
async function loadEventsFromDirectory(eventsPath, client, eventCount = 0) {
    const items = readdirSync(eventsPath);

    for (const item of items) {
        const itemPath = join(eventsPath, item);
        const stat = statSync(itemPath);

        if (stat.isDirectory()) {
            // Se for diretório, carrega recursivamente
            eventCount = await loadEventsFromDirectory(itemPath, client, eventCount);
        } else if (item.endsWith('.js')) {
            // Se for arquivo .js, carrega o evento
            try {
                const fileUrl = pathToFileURL(itemPath).href;
                const event = await import(fileUrl);
                
                if (event.default && event.default.name && event.default.execute) {
                    // Registra o evento
                    if (event.default.once) {
                        client.once(event.default.name, (...args) => event.default.execute(...args));
                    } else {
                        client.on(event.default.name, (...args) => event.default.execute(...args));
                    }
                    
                    eventCount++;
                    logger.debug(`Evento carregado: ${event.default.name} (${item}) - ${event.default.once ? 'once' : 'on'}`);
                } else {
                    logger.warn(`⚠️ Evento inválido encontrado: ${item} - faltando propriedades obrigatórias`);
                }
            } catch (error) {
                logger.error(`❌ Erro ao carregar evento ${item}: ${error.message}`);
                console.error(error.stack);
            }
        }
    }

    return eventCount;
}

/**
 * Carrega todos os eventos da pasta events
 * @param {Client} client - Instância do cliente Discord
 */
export async function loadEvents(client) {
    try {
        const eventsPath = join(__dirname, '..', 'events');
        const eventCount = await loadEventsFromDirectory(eventsPath, client);

        logger.info(`✅ ${eventCount} eventos carregados com sucesso`);
        
    } catch (error) {
        logger.error('❌ Erro ao carregar eventos:', error);
        throw error;
    }
}
