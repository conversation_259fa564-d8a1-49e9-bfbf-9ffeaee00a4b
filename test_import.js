import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import { access, constants } from 'node:fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Testing imports...');

async function testImport(modulePath, timeout = 5000) {
  const startTime = Date.now();
  const fileName = path.basename(modulePath);
  
  try {
    // Verificar se o arquivo existe
    await access(modulePath, constants.F_OK);
    
    // Criar promise com timeout
    const moduleUrl = pathToFileURL(modulePath).href;
    const importPromise = import(moduleUrl);
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Import timeout')), timeout)
    );
    
    await Promise.race([importPromise, timeoutPromise]);
    
    const duration = Date.now() - startTime;
    console.log(`✅ ${fileName} OK (${duration}ms)`);
    return { success: true, duration, file: fileName };
  } catch (e) {
    const duration = Date.now() - startTime;
    console.error(`❌ Error in ${fileName}: ${e.message}`);
    return { success: false, duration, file: fileName, error: e.message };
  }
}

(async () => {
  const testStartTime = Date.now();
  const results = [];
  
  const modules = [
    path.join(__dirname, 'src', 'handlers', 'slashCommandHandler.js'),
    path.join(__dirname, 'src', 'handlers', 'autocompleteHandler.js'),
    path.join(__dirname, 'src', 'handlers', 'buttonHandler.js'),
    path.join(__dirname, 'src', 'handlers', 'selectMenuHandler.js'),
    path.join(__dirname, 'src', 'handlers', 'modalHandler.js'),
    path.join(__dirname, 'src', 'utils', 'logger.js'),
    path.join(__dirname, 'src', 'events', 'interactionCreate.js')
  ];
  
  for (const modulePath of modules) {
    const result = await testImport(modulePath);
    results.push(result);
  }
  
  // Relatório final
  const totalTime = Date.now() - testStartTime;
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const avgTime = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
  
  console.log('\n📊 Test Summary:');
  console.log(`✅ Successful: ${successful}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`⏱️  Total time: ${totalTime}ms`);
  console.log(`📈 Average time per import: ${Math.round(avgTime)}ms`);
  
  if (failed > 0) {
    console.log('\n❌ Failed imports:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`   ${r.file}: ${r.error}`);
    });
  }
  
  console.log(`\n${failed === 0 ? '🎉' : '⚠️'} Test complete.`);
})();