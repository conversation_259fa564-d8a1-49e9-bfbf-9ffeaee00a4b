import dotenv from 'dotenv';
import mongoose from 'mongoose';
import Product from '../src/models/Product.js';
import Store from '../src/models/Store.js';

// Carrega variáveis de ambiente
dotenv.config();

async function testProductCreation() {
    try {
        console.log('🧪 Testando criação de produto...');
        
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB');
        
        // Busca uma loja existente para usar no teste
        const existingStore = await Store.findOne();
        
        if (!existingStore) {
            console.log('⚠️  Nenhuma loja encontrada. Criando loja de teste...');
            
            const testStore = new Store({
                name: 'Loja Teste',
                description: 'Loja para teste de criação de produto',
                banner: 'https://via.placeholder.com/800x200',
                color: '#3498db',
                guildId: '123456789',
                channelId: '987654321',
                messageId: '111222333',
                createdBy: 'test-user'
            });
            
            await testStore.save();
            console.log('✅ Loja de teste criada');
        }
        
        const store = existingStore || await Store.findOne();
        
        // Tenta criar um produto de teste
        console.log('📦 Criando produto de teste...');
        
        const testProduct = new Product({
            name: 'Produto Teste - ' + Date.now(),
            description: 'Produto criado para testar se o erro E11000 foi corrigido',
            price: 19.99,
            stock: 0,
            category: 'digital',
            status: 'out_of_stock',
            createdBy: 'test-user-' + Date.now(),
            storeId: store._id,
            emoji: '🧪'
        });
        
        await testProduct.save();
        console.log('✅ Produto criado com sucesso!');
        console.log(`   ID: ${testProduct._id}`);
        console.log(`   Nome: ${testProduct.name}`);
        console.log(`   Preço: R$ ${testProduct.price}`);
        
        // Tenta criar outro produto para garantir que não há mais conflito
        console.log('📦 Criando segundo produto de teste...');
        
        const testProduct2 = new Product({
            name: 'Produto Teste 2 - ' + Date.now(),
            description: 'Segundo produto para confirmar que o erro foi corrigido',
            price: 29.99,
            stock: 0,
            category: 'digital',
            status: 'out_of_stock',
            createdBy: 'test-user-2-' + Date.now(),
            storeId: store._id,
            emoji: '🎮'
        });
        
        await testProduct2.save();
        console.log('✅ Segundo produto criado com sucesso!');
        console.log(`   ID: ${testProduct2._id}`);
        console.log(`   Nome: ${testProduct2.name}`);
        
        // Limpa os produtos de teste
        console.log('🧹 Limpando produtos de teste...');
        await Product.deleteMany({ 
            name: { $regex: /^Produto Teste/ }
        });
        console.log('✅ Produtos de teste removidos');
        
        await mongoose.disconnect();
        console.log('✅ Teste concluído com sucesso!');
        console.log('🎉 O erro E11000 duplicate key error foi corrigido!');
        
    } catch (error) {
        console.error('❌ Erro durante o teste:', error);
        
        if (error.code === 11000) {
            console.error('💥 ERRO E11000 ainda presente!');
            console.error('   Verifique se o script de correção foi executado corretamente');
        }
        
        process.exit(1);
    }
}

testProductCreation();
