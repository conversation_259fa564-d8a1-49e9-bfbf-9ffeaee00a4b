import { SlashCommandBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder, ActionRowBuilder } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import Store from '../../models/Store.js';

export default {
    data: new SlashCommandBuilder()
        .setName('deletar-loja')
        .setDescription('Deleta uma loja existente no servidor (apenas administradores)'),
    
    async execute(interaction) {
        try {
            // Verificação se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem executar este comando.',
                    ephemeral: true
                });
            }

            // Busca lojas ativas no servidor atual
            const stores = await Store.findByGuild(interaction.guild.id);

            if (stores.length === 0) {
                return await interaction.reply({
                    content: '❌ Não há lojas criadas neste servidor. Use `/criar-loja` para criar uma nova loja.',
                    ephemeral: true
                });
            }

            // Cria o dropdown com as lojas disponíveis
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('delete_store_select')
                .setPlaceholder('Selecione uma loja para deletar...')
                .setMinValues(1)
                .setMaxValues(1)
                .addOptions(
                    stores.map(store => 
                        new StringSelectMenuOptionBuilder()
                            .setLabel(store.name)
                            .setDescription(`Canal: #${store.channelId} | Criada em: ${store.createdAt.toLocaleDateString('pt-BR')}`)
                            .setValue(store._id.toString())
                            .setEmoji('🗑️')
                    )
                )
                .addOptions(
                    new StringSelectMenuOptionBuilder()
                        .setLabel('❌ Cancelar Seleção')
                        .setDescription('Cancelar a seleção')
                        .setValue('cancel')
                );

            const selectRow = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.reply({
                content: '🗑️ **Deletar Loja**\n\n⚠️ **ATENÇÃO:** Esta ação é irreversível e irá:\n' +
                        '• Deletar o canal da loja\n' +
                        '• Remover todos os dados da loja\n' +
                        '• Apagar todas as mensagens relacionadas\n\n' +
                        'Selecione a loja que deseja deletar:',
                components: [selectRow],
                ephemeral: true
            });

            logger.info(`Comando deletar-loja executado por ${interaction.user.tag} em ${interaction.guild.name}`);

        } catch (error) {
            logger.error('Erro ao executar comando deletar-loja:', error);
            
            const errorMessage = {
                content: '❌ Erro ao carregar as lojas. Tente novamente mais tarde.',
                ephemeral: true
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    }
};
