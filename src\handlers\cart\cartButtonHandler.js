import { <PERSON>bed<PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import ShoppingCart from '../../models/ShoppingCart.js';
import Product from '../../models/Product.js';
import StockItem from '../../models/StockItem.js';
import { fixedCartEmbedHandler } from './fixedCartEmbedHandler.js';
import { pixPaymentManager } from '../../utils/managers/pixPaymentManager.js';
import { COLORS, BOT_CONFIG } from '../../config/constants.js';
import { getEmojiFromInteraction, getEmoji } from '../../utils/ui/emojiHelper.js';


/**
 * Handler para botões do carrinho de compras
 */
export class CartButtonHandler {
    /**
     * Processa interações de botões do carrinho
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCartButton(interaction) {
        const customId = interaction.customId;

        try {
            switch (customId) {
                case 'cart_clear':
                    await this.handleClearCart(interaction);
                    break;
                case 'cart_checkout':
                    await this.handleCheckout(interaction);
                    break;
                case 'cart_check_payment_status':
                    await this.handleCheckPaymentStatus(interaction);
                    break;
                case 'cart_cancel_payment':
                    await this.handleCancelPayment(interaction);
                    break;
                case 'cart_cancel_purchase':
                    await this.handleCancelPurchase(interaction);
                    break;
                case 'cart_copy_pix':
                    await this.handleCopyPix(interaction);
                    break;
                case 'cart_confirm_cancel':
                    await this.handleConfirmCancel(interaction);
                    break;
                case 'cart_dismiss_cancel':
                    await this.handleDismissCancel(interaction);
                    break;
                case 'cart_manage_items':
                    await this.handleManageItems(interaction);
                    break;
                default:
                    logger.warn(`Botão de carrinho não reconhecido: ${customId}`);
                    const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                    await interaction.reply({
                        content: `${errorEmoji} Ação não reconhecida.`,
                        ephemeral: true
                    });
            }
        } catch (error) {
            logger.error('Erro ao processar botão do carrinho:', error);
            
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            const errorMessage = {
                content: `${errorEmoji} Ocorreu um erro ao processar sua solicitação.`,
                ephemeral: true
            };

            try {
                if (interaction.replied) {
                    await interaction.followUp(errorMessage);
                } else if (interaction.deferred) {
                    await interaction.editReply(errorMessage);
                } else {
                    await interaction.reply(errorMessage);
                }
            } catch (replyError) {
                logger.error(`Erro ao enviar resposta de erro para botão do carrinho ${customId}:`, replyError);
            }
        }
    }

    /**
     * Limpa o carrinho de compras
     * @param {Object} interaction - Interação do Discord
     */
    static async handleClearCart(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id, 
                interaction.guild.id
            );

            if (!cart) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.editReply({
                    content: `${errorEmoji} Nenhum carrinho ativo encontrado.`
                });
            }

            // Limpa o carrinho
            await cart.clearCart();

            // Atualiza embed fixo para mostrar carrinho vazio
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(
                interaction.channel,
                cart
            );

            const successEmoji = await getEmojiFromInteraction(interaction, 'SUCCESS');
            await interaction.editReply({
                content: `${successEmoji} Carrinho limpo com sucesso!`
            });

            logger.info(`Carrinho limpo pelo usuário ${interaction.user.tag}`);

        } catch (error) {
            logger.error('Erro ao limpar carrinho:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.editReply({
                content: `${errorEmoji} Erro ao limpar o carrinho.`
            });
        }
    }

    /**
     * Inicia processo de checkout
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCheckout(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id, 
                interaction.guild.id
            );

            if (!cart || !cart.items || cart.items.length === 0) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.editReply({
                    content: `${errorEmoji} Seu carrinho está vazio.`
                });
            }

            // Valida valor mínimo
            if (cart.subtotal < BOT_CONFIG.STORE.MIN_ORDER_VALUE) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.editReply({
                    content: `${errorEmoji} Valor mínimo para pedido: ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${BOT_CONFIG.STORE.MIN_ORDER_VALUE.toFixed(2)}`
                });
            }

            // Cria pagamento PIX
            const paymentData = {
                amount: cart.subtotal,
                description: `Compra na loja ${cart.storeName}`,
                payerName: interaction.user.username,
                payerEmail: `${interaction.user.id}@discord.com`,
                externalReference: `cart_${cart._id}`
            };

            const pixPayment = await pixPaymentManager.createPixPayment(
                paymentData,
                interaction.guild.id
            );

            // Salva paymentId e status no carrinho
            cart.paymentId = pixPayment.id;
            cart.paymentStatus = 'pending';
            await cart.save();

            // Atualiza embed com informações de pagamento
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(
                interaction.channel,
                cart,
                {
                    paymentInfo: pixPayment,
                    isPaymentPending: true
                }
            );

            // Configura cancelamento automático em 5 minutos
            pixPaymentManager.setupAutoCancelPayment(
                pixPayment.id,
                interaction.guild.id,
                async (cancelInfo) => {
                    await this.handleAutoCancelPayment(interaction.channel, cart, cancelInfo);
                }
            );

            // Inicia polling do pagamento
            await pixPaymentManager.startPaymentPolling(
                pixPayment.id,
                interaction.guild.id,
                async (status) => {
                    await this.handlePaymentStatusChange(
                        interaction.channel,
                        cart,
                        status
                    );
                },
                {
                    onTimeout: async () => {
                        await this.handlePaymentTimeout(interaction.channel, cart);
                    }
                }
            );

            // Resposta simples confirmando que o pagamento foi gerado
            const successEmoji = await getEmojiFromInteraction(interaction, 'SUCCESS');
            await interaction.editReply({
                content: `${successEmoji} **Pagamento PIX gerado com sucesso!** O QR Code foi adicionado ao carrinho acima.`,
                ephemeral: true
            });

            logger.info(`Checkout iniciado para carrinho ${cart._id} - Pagamento: ${pixPayment.id}`);

        } catch (error) {
            logger.error('Erro no checkout:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.editReply({
                content: `${errorEmoji} Erro ao processar checkout. Verifique se o MercadoPago está configurado.`
            });
        }
    }

    /**
     * Manipula a verificação de status do pagamento
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCheckPaymentStatus(interaction) {
        try {
            await interaction.deferReply({ ephemeral: true });

            // Buscar carrinho do usuário
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id,
                interaction.guild.id
            );

            if (!cart || !cart.paymentId) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                await interaction.editReply({
                    content: `${errorEmoji} Nenhum pagamento ativo encontrado.`,
                    ephemeral: true
                });
                return;
            }

            // Verificar status no MercadoPago
            const paymentStatus = await pixPaymentManager.checkPaymentStatus(cart.paymentId, interaction.guild.id);
            
            if (!paymentStatus) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                await interaction.editReply({
                    content: `${errorEmoji} Erro ao verificar status do pagamento. Tente novamente.`,
                    ephemeral: true
                });
                return;
            }

            // Calcular tempo restante
            const now = new Date();
            const expirationDate = new Date(paymentStatus.date_of_expiration);
            const timeRemaining = expirationDate > now ? 
                Math.ceil((expirationDate - now) / (1000 * 60)) : 0;

            const pendingEmoji = await getEmojiFromInteraction(interaction, 'PENDING');
            const successEmoji = await getEmojiFromInteraction(interaction, 'SUCCESS');
            const cancelledEmoji = await getEmojiFromInteraction(interaction, 'CANCELLED');
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            const unknownEmoji = await getEmojiFromInteraction(interaction, 'UNKNOWN');
            
            const statusEmoji = paymentStatus.status === 'pending' ? pendingEmoji : 
                               paymentStatus.status === 'approved' ? successEmoji : 
                               paymentStatus.status === 'cancelled' ? cancelledEmoji : 
                               paymentStatus.status === 'rejected' ? errorEmoji : unknownEmoji;
            
            const statusText = paymentStatus.status === 'pending' ? 'Aguardando Pagamento' : 
                              paymentStatus.status === 'approved' ? 'Pagamento Aprovado' : 
                              paymentStatus.status === 'cancelled' ? 'Pagamento Cancelado' : 
                              paymentStatus.status === 'rejected' ? 'Pagamento Rejeitado' : 'Status Desconhecido';

            const searchEmoji = await getEmojiFromInteraction(interaction, 'SEARCH');
            const moneyEmoji = await getEmojiFromInteraction(interaction, 'MONEY');
            const idEmoji = await getEmojiFromInteraction(interaction, 'ID');
            const timeEmoji = await getEmojiFromInteraction(interaction, 'TIME');
            const calendarEmoji = await getEmojiFromInteraction(interaction, 'CALENDAR');
            
            const embed = new EmbedBuilder()
                .setTitle(`${searchEmoji} Status do Pagamento`)
                .setDescription(`${statusEmoji} **Status:** ${statusText}\n\n` +
                    `${moneyEmoji} **Valor:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${paymentStatus.transaction_amount.toFixed(2)}\n` +
                    `${idEmoji} **ID:** \`${paymentStatus.id}\`\n` +
                    `${timeEmoji} **Tempo restante:** ${timeRemaining > 0 ? `${timeRemaining} minutos` : 'Expirado'}\n` +
                    `${calendarEmoji} **Criado em:** <t:${Math.floor(new Date(paymentStatus.date_created).getTime() / 1000)}:f>`)
                .setColor(paymentStatus.status === 'approved' ? COLORS.SUCCESS : 
                         paymentStatus.status === 'pending' ? COLORS.WARNING : COLORS.ERROR)
                .setTimestamp();

            await interaction.editReply({
                embeds: [embed],
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro ao verificar status do pagamento:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.editReply({
                content: `${errorEmoji} Erro interno ao verificar status. Tente novamente.`,
                ephemeral: true
            });
        }
    }

    /**
     * Mostra confirmação para cancelar pagamento
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCancelPayment(interaction) {
        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id,
                interaction.guild.id
            );

            if (!cart) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.reply({
                    content: `${errorEmoji} Nenhum carrinho ativo encontrado.`,
                    ephemeral: true
                });
            }

            // Cria botões de confirmação
            const successEmoji = await getEmojiFromInteraction(interaction, 'SUCCESS');
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            const confirmRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('cart_confirm_cancel')
                        .setLabel('Sim, cancelar compra')
                        .setEmoji(successEmoji || '✅')
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('cart_dismiss_cancel')
                        .setLabel('Não, continuar')
                        .setEmoji(errorEmoji || '❌')
                        .setStyle(ButtonStyle.Secondary)
                );

            const warningEmoji = await getEmojiFromInteraction(interaction, 'WARNING');
            await interaction.reply({
                content:
                    `${warningEmoji} **Confirmar cancelamento**\n\n` +
                    `Tem certeza que deseja cancelar esta compra?\n` +
                    `• O pagamento PIX será cancelado\n` +
                    `• Este canal será fechado automaticamente\n` +
                    `• Você perderá todos os itens do carrinho`,
                components: [confirmRow],
                ephemeral: true
            });

            logger.info(`Confirmação de cancelamento solicitada por ${interaction.user.tag}`);

        } catch (error) {
            logger.error('Erro ao mostrar confirmação de cancelamento:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.reply({
                content: `${errorEmoji} Erro ao processar solicitação.`,
                ephemeral: true
            });
        }
    }

    /**
     * Confirma cancelamento e fecha o canal
     * @param {Object} interaction - Interação do Discord
     */
    static async handleConfirmCancel(interaction) {
        await interaction.deferUpdate();

        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id,
                interaction.guild.id
            );

            if (cart) {
                // Tentar cancelar pagamento no MercadoPago se existir
                if (cart.paymentId) {
                    try {
                        const cancelResult = await pixPaymentManager.cancelPayment(cart.paymentId);
                        if (cancelResult.success) {
                            logger.info(`Pagamento ${cart.paymentId} cancelado com sucesso`);
                        } else {
                            logger.warn(`Não foi possível cancelar pagamento ${cart.paymentId}: ${cancelResult.message}`);
                        }
                    } catch (error) {
                        logger.error(`Erro ao cancelar pagamento ${cart.paymentId}:`, error);
                    }

                    // Parar polling do pagamento
                    try {
                        await pixPaymentManager.stopPaymentPolling(cart.paymentId);
                        logger.info(`Polling do pagamento ${cart.paymentId} interrompido`);
                    } catch (error) {
                        logger.error(`Erro ao parar polling do pagamento ${cart.paymentId}:`, error);
                    }
                }

                // Atualizar status do carrinho
                cart.paymentStatus = 'cancelled';
                await cart.markAsAbandoned();
                logger.info(`Carrinho ${cart._id} marcado como abandonado pelo usuário ${interaction.user.id}`);
            }

            // Remove embed fixo
            await fixedCartEmbedHandler.removeCartEmbed(interaction.channel.id);

            // Envia mensagem de cancelamento no canal
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            const cancelEmbed = new EmbedBuilder()
                .setColor(COLORS.ERROR)
                .setTitle(`${errorEmoji} Compra Cancelada`)
                .setDescription(
                    `${errorEmoji} **Sua compra foi cancelada com sucesso.**\n\n` +
                    `• O pagamento PIX foi cancelado\n` +
                    `• Todos os itens foram removidos do carrinho\n` +
                    `• Este canal será fechado em alguns segundos`
                )
                .setTimestamp();

            await interaction.channel.send({ embeds: [cancelEmbed] });

            await interaction.editReply({
                embeds: [cancelEmbed],
                components: []
            });

            // Fechar o canal imediatamente após o cancelamento
            setTimeout(async () => {
                try {
                    await interaction.channel.delete('Compra cancelada pelo usuário');
                } catch (error) {
                    logger.error('Erro ao fechar canal após cancelamento:', error);
                }
            }, 3000); // Reduzido para 3 segundos

            logger.info(`Compra cancelada e canal agendado para fechamento - usuário: ${interaction.user.tag}`);

        } catch (error) {
            logger.error('Erro ao confirmar cancelamento:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.editReply({
                content: `${errorEmoji} Erro ao cancelar compra.`,
                components: []
            });
        }
    }

    /**
     * Dismisses cancelamento e volta ao estado normal
     * @param {Object} interaction - Interação do Discord
     */
    static async handleDismissCancel(interaction) {
        try {
            const successEmoji = await getEmojiFromInteraction(interaction, 'SUCCESS');
            await interaction.update({
                content: `${successEmoji} Cancelamento descartado. Continue com sua compra!`,
                components: []
            });

            logger.info(`Cancelamento descartado por ${interaction.user.tag}`);

        } catch (error) {
            logger.error('Erro ao descartar cancelamento:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.reply({
                content: `${errorEmoji} Erro ao processar solicitação.`,
                ephemeral: true
            });
        }
    }

    /**
     * Abre modal para gerenciar itens do carrinho
     * @param {Object} interaction - Interação do Discord
     */
    static async handleManageItems(interaction) {
        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id, 
                interaction.guild.id
            );

            if (!cart || !cart.items || cart.items.length === 0) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.reply({
                    content: `${errorEmoji} Seu carrinho está vazio.`,
                    ephemeral: true
                });
            }

            // Cria modal para gerenciar quantidades
            const modal = new ModalBuilder()
                .setCustomId('cart_manage_modal')
                .setTitle('Gerenciar Itens do Carrinho');

            // Adiciona campos para cada item (máximo 5 por limitação do Discord)
            const itemsToShow = cart.items.slice(0, 5);
            
            itemsToShow.forEach((item, index) => {
                const input = new TextInputBuilder()
                    .setCustomId(`item_quantity_${index}`)
                    .setLabel(`${item.productName} (atual: ${item.quantity})`)
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder('Digite a nova quantidade (0 para remover)')
                    .setValue(item.quantity.toString())
                    .setRequired(true)
                    .setMaxLength(2);

                const actionRow = new ActionRowBuilder().addComponents(input);
                modal.addComponents(actionRow);
            });

            await interaction.showModal(modal);

        } catch (error) {
            logger.error('Erro ao abrir modal de gerenciamento:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.reply({
                content: `${errorEmoji} Erro ao abrir gerenciamento de itens.`,
                ephemeral: true
            });
        }
    }

    /**
     * Manipula a cópia do código PIX
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCopyPix(interaction) {
        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id,
                interaction.guild.id
            );

            if (!cart || !cart.paymentId) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.reply({
                    content: `${errorEmoji} Nenhum pagamento PIX ativo encontrado.`,
                    ephemeral: true
                });
            }

            // Busca informações do pagamento
            const paymentStatus = await pixPaymentManager.checkPaymentStatus(cart.paymentId, interaction.guild.id);

            if (!paymentStatus) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.reply({
                    content: `${errorEmoji} Erro ao buscar informações do pagamento.`,
                    ephemeral: true
                });
            }

            // Busca o código PIX do pagamento original
            const paymentDetails = await pixPaymentManager.getPaymentDetails(cart.paymentId, interaction.guild.id);

            if (!paymentDetails || !paymentDetails.qrCodeString) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.reply({
                    content: `${errorEmoji} Código PIX não disponível para este pagamento.`,
                    ephemeral: true
                });
            }

            // Envia o código PIX em uma mensagem privada para facilitar a cópia
            const pixEmoji = await getEmojiFromInteraction(interaction, 'PIX');
            const clipboardEmoji = await getEmojiFromInteraction(interaction, 'CLIPBOARD');
            const moneyEmoji = await getEmojiFromInteraction(interaction, 'MONEY');
            const timeEmoji = await getEmojiFromInteraction(interaction, 'TIME');
            
            const embed = new EmbedBuilder()
                .setColor(COLORS.PRIMARY)
                .setTitle(`${pixEmoji} Código PIX para Cópia`)
                .setDescription(
                    `${clipboardEmoji} **Copie o código abaixo e cole no seu app do banco:**\n\n` +
                    `\`\`\`\n${paymentDetails.qrCodeString}\n\`\`\`\n\n` +
                    `${moneyEmoji} **Valor:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${cart.subtotal.toFixed(2)}\n` +
                    `${timeEmoji} **Válido até:** <t:${Math.floor(new Date(paymentDetails.expirationDate).getTime() / 1000)}:R>`
                )
                .setFooter({
                    text: 'Selecione o código acima e copie (Ctrl+C)'
                });

            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });

            logger.info(`Código PIX copiado pelo usuário ${interaction.user.tag} - Pagamento: ${cart.paymentId}`);

        } catch (error) {
            logger.error('Erro ao processar cópia do PIX:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.reply({
                content: `${errorEmoji} Erro ao buscar código PIX. Tente novamente.`,
                ephemeral: true
            });
        }
    }

    /**
     * Manipula o cancelamento da compra a partir do embed de boas-vindas
     * @param {Object} interaction - Interação do Discord
     */
    static async handleCancelPurchase(interaction) {
        try {
            // Busca carrinho ativo
            const cart = await ShoppingCart.findActiveByUser(
                interaction.user.id,
                interaction.guild.id
            );

            if (!cart) {
                const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
                return await interaction.reply({
                    content: `${errorEmoji} Nenhum carrinho ativo encontrado.`,
                    ephemeral: true
                });
            }

            // Cria botões de confirmação
            const confirmRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('cart_confirm_cancel')
                        .setLabel('Sim, cancelar compra')
                        .setEmoji('✅')
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('cart_dismiss_cancel')
                        .setLabel('Não, continuar')
                        .setEmoji('❌')
                        .setStyle(ButtonStyle.Secondary)
                );

            const warningEmoji = await getEmojiFromInteraction(interaction, 'WARNING');
            const embed = new EmbedBuilder()
                .setColor(COLORS.WARNING)
                .setTitle(`${warningEmoji} Confirmar Cancelamento`)
                .setDescription(
                    `${warningEmoji} **Tem certeza que deseja cancelar sua compra?**\n\n` +
                    `Esta ação irá:\n` +
                    `• Limpar completamente seu carrinho\n` +
                    `• Fechar este canal de compras\n` +
                    `• Cancelar qualquer pagamento pendente\n\n` +
                    `**Esta ação não pode ser desfeita.**`
                )
                .setFooter({
                    text: 'Escolha uma opção abaixo'
                });

            await interaction.reply({
                embeds: [embed],
                components: [confirmRow],
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro ao processar cancelamento da compra:', error);
            const errorEmoji = await getEmojiFromInteraction(interaction, 'ERROR');
            await interaction.reply({
                content: `${errorEmoji} Erro ao processar cancelamento. Tente novamente.`,
                ephemeral: true
            });
        }
    }

    /**
     * Processa mudança de status do pagamento
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     * @param {Object} status - Status do pagamento
     */
    static async handlePaymentStatusChange(channel, cart, status) {
        try {
            // Atualiza status no carrinho
            cart.paymentStatus = status.status;
            await cart.save();

            // Se é apenas uma atualização de progresso, não faz nada mais
            if (status.isProgressUpdate) {
                return;
            }

            // Para o polling e remove timeout de cancelamento quando pagamento é finalizado
            if (['approved', 'rejected', 'cancelled'].includes(status.status)) {
                pixPaymentManager.stopPaymentPolling(cart.paymentId);
                // O stopPaymentPolling já remove o timeout, mas garantimos aqui também
                pixPaymentManager.clearPaymentTimeout(cart.paymentId);
            }
            
            // Atualizar status do pagamento no carrinho
            if (status.approved) {
                // Atualizar embed com novo status
                await fixedCartEmbedHandler.createOrUpdateCartEmbed(channel, cart, {
                    isPaymentPending: false,
                    paymentInfo: null
                });
                
                // Pagamento aprovado
                const successEmoji = await getEmoji(channel.guild.id, 'SUCCESS');
                const moneyEmoji = await getEmoji(channel.guild.id, 'MONEY');
                const packageEmoji = await getEmoji(channel.guild.id, 'PACKAGE');
                const celebrationEmoji = await getEmoji(channel.guild.id, 'CELEBRATION');
                
                const embed = new EmbedBuilder()
                    .setColor(COLORS.SUCCESS)
                    .setTitle(`${successEmoji} Pagamento Confirmado!`)
                    .setDescription(
                        `${successEmoji} **Seu pagamento foi confirmado com sucesso!**\n\n` +
                        `${moneyEmoji} **Valor:** ${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${cart.subtotal.toFixed(2)}\n` +
                        `${packageEmoji} **Itens:** ${cart.itemCount}\n\n` +
                        `${celebrationEmoji} **Próximos passos:**\n` +
                        `• Seus itens foram processados\n` +
                        `• Você receberá os produtos em breve\n` +
                        `• Este canal será fechado automaticamente`
                    )
                    .setTimestamp();

                await channel.send({ embeds: [embed] });

                // Entrega automática dos produtos
                await this.deliverProducts(cart, channel);

                // Marca carrinho como completo
                await cart.markAsCompleted();
                logger.info(`Pagamento confirmado para carrinho ${cart._id}`);
                
            } else if (status.rejected || status.cancelled) {
                // Atualizar status no carrinho
                cart.paymentStatus = status.rejected ? 'rejected' : 'cancelled';
                await cart.save();
                
                // Atualizar embed com novo status
                await fixedCartEmbedHandler.createOrUpdateCartEmbed(channel, cart, {
                    isPaymentPending: false,
                    paymentInfo: null
                });

                const errorEmoji = await getEmoji(channel.guild.id, 'ERROR');
                const embed = new EmbedBuilder()
                    .setColor(COLORS.ERROR)
                    .setTitle(`${errorEmoji} Pagamento ${status.rejected ? 'Rejeitado' : 'Cancelado'}`)
                    .setDescription(
                        `${errorEmoji} **Seu pagamento não foi processado.**\n\n` +
                        `🔄 **Você pode tentar novamente:**\n` +
                        `• Clique em "Finalizar Compra" novamente\n` +
                        `• Verifique os dados do pagamento\n` +
                        `• Entre em contato se o problema persistir`
                    )
                    .setTimestamp();

                await channel.send({ embeds: [embed] });
                logger.info(`Pagamento ${status.status} para carrinho ${cart._id}`);
                
            } else if (status.isProgressUpdate) {
                // Apenas uma atualização de progresso - não envia embed separado
                // O status permanece no embed principal
                logger.info(`Atualização de progresso do pagamento ${cart.paymentId}: ${status.attempts} tentativas`);
            }

        } catch (error) {
            logger.error('Erro ao processar mudança de status:', error);
        }
    }

    /**
     * Processa cancelamento automático do pagamento (5 minutos)
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     * @param {Object} cancelInfo - Informações do cancelamento
     */
    static async handleAutoCancelPayment(channel, cart, cancelInfo) {
        try {
            // Atualizar status do carrinho para cancelado
            cart.paymentStatus = 'cancelled';
            await cart.save();
            
            // Para o polling se ainda estiver ativo
            pixPaymentManager.stopPaymentPolling(cancelInfo.paymentId);
            
            // Atualizar embed com status cancelado
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(channel, cart, {
                isPaymentPending: false,
                paymentInfo: null
            });

            const warningEmoji = await getEmoji(channel.guild.id, 'WARNING');
            const timeEmoji = await getEmoji(channel.guild.id, 'TIME');
            const cancelEmoji = await getEmoji(channel.guild.id, 'CANCEL');
            const refreshEmoji = await getEmoji(channel.guild.id, 'REFRESH');
            const lightbulbEmoji = await getEmoji(channel.guild.id, 'LIGHTBULB');
            
            const embed = new EmbedBuilder()
                .setColor(COLORS.DANGER)
                .setTitle(`${cancelEmoji} Pagamento Cancelado Automaticamente`)
                .setDescription(
                    `${timeEmoji} **Seu pagamento PIX foi cancelado após 5 minutos sem confirmação.**\n\n` +
                    `${warningEmoji} **O que aconteceu:**\n` +
                    `• O pagamento não foi confirmado em 5 minutos\n` +
                    `• O PIX foi automaticamente cancelado no MercadoPago\n` +
                    `• Seus itens ainda estão no carrinho\n\n` +
                    `${refreshEmoji} **Para continuar sua compra:**\n` +
                    `• Clique em "Finalizar Compra" novamente\n` +
                    `• Um novo QR Code será gerado\n` +
                    `• Complete o pagamento em até 5 minutos\n\n` +
                    `${lightbulbEmoji} **Dica:** Tenha seu app do banco pronto antes de gerar o próximo QR Code!`
                )
                .setTimestamp();

            await channel.send({ embeds: [embed] });

            logger.info(`Pagamento ${cancelInfo.paymentId} cancelado automaticamente após 5 minutos para carrinho ${cart._id}`);

        } catch (error) {
            logger.error('Erro ao processar cancelamento automático de pagamento:', error);
        }
    }

    /**
     * Processa timeout do pagamento
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     */
    static async handlePaymentTimeout(channel, cart) {
        try {
            // Atualizar status do carrinho para expirado
            cart.paymentStatus = 'expired';
            await cart.save();
            
            // Atualizar embed com status expirado
            await fixedCartEmbedHandler.createOrUpdateCartEmbed(channel, cart, {
                isPaymentPending: false,
                paymentInfo: null
            });

            const warningEmoji = await getEmoji(channel.guild.id, 'WARNING');
            const timeEmoji = await getEmoji(channel.guild.id, 'TIME');
            const errorEmoji = await getEmoji(channel.guild.id, 'ERROR');
            const refreshEmoji = await getEmoji(channel.guild.id, 'REFRESH');
            const lightbulbEmoji = await getEmoji(channel.guild.id, 'LIGHTBULB');
            
            const embed = new EmbedBuilder()
                .setColor(COLORS.WARNING)
                .setTitle(`${warningEmoji} Tempo Limite do Pagamento PIX Atingido`)
                .setDescription(
                    `${timeEmoji} **O prazo de 15 minutos para pagamento PIX expirou.**\n\n` +
                    `${errorEmoji} **O que aconteceu:**\n` +
                    `• O QR Code PIX não é mais válido\n` +
                    `• O pagamento foi automaticamente cancelado pelo MercadoPago\n` +
                    `• Seus itens ainda estão no carrinho\n\n` +
                    `${refreshEmoji} **Para continuar sua compra:**\n` +
                    `• Clique em "Finalizar Compra" novamente\n` +
                    `• Um novo QR Code será gerado\n` +
                    `• Complete o pagamento em até 15 minutos\n\n` +
                    `${lightbulbEmoji} **Dica:** Tenha seu app do banco pronto antes de gerar o próximo QR Code!`
                )
                .setTimestamp();

            await channel.send({ embeds: [embed] });

            logger.info(`Pagamento expirado naturalmente para carrinho ${cart._id} - timeout do MercadoPago`);

        } catch (error) {
            logger.error('Erro ao processar timeout de pagamento:', error);
        }
    }

    /**
     * Entrega automática dos produtos após confirmação de pagamento
     * @param {Object} cart - Carrinho de compras
     * @param {Object} channel - Canal do Discord
     */
    static async deliverProducts(cart, channel) {
        try {
            const user = await channel.client.users.fetch(cart.userId);
            const deliveredProducts = [];
            
            // Processa cada item do carrinho
            for (const item of cart.items) {
                const product = await Product.findById(item.productId);
                if (!product) {
                    logger.warn(`Produto ${item.productId} não encontrado para entrega`);
                    continue;
                }

                // Busca itens de estoque disponíveis para este produto
                const stockItems = await StockItem.findAvailableByProduct(item.productId)
                    .limit(item.quantity);

                if (stockItems.length < item.quantity) {
                    logger.error(`Estoque insuficiente para produto ${product.name}. Necessário: ${item.quantity}, Disponível: ${stockItems.length}`);
                    continue;
                }

                // Marca os itens como vendidos e coleta o conteúdo
                const productContent = [];
                for (const stockItem of stockItems) {
                    await stockItem.markAsSold(cart.userId, cart.orderId);
                    productContent.push(stockItem.content);
                }

                // Atualiza estatísticas do produto
                await product.recordSale(item.quantity);

                deliveredProducts.push({
                    name: product.name,
                    emoji: product.emoji || item.productEmoji || '📦',
                    quantity: item.quantity,
                    content: productContent
                });
            }

            if (deliveredProducts.length === 0) {
                logger.warn(`Nenhum produto foi entregue para o carrinho ${cart._id}`);
                return;
            }

            // Tenta enviar por DM primeiro
            try {
                await this.sendProductsByDM(user, deliveredProducts, cart);
                logger.info(`Produtos entregues por DM para usuário ${cart.userId}`);
                
                // Confirma entrega no canal
                const deliveryEmoji = await getEmoji(channel.guild.id, 'SUCCESS');
                const dmEmoji = await getEmoji(channel.guild.id, 'MAIL');
                
                const confirmEmbed = new EmbedBuilder()
                    .setColor(COLORS.SUCCESS)
                    .setTitle(`${deliveryEmoji} Produtos Entregues!`)
                    .setDescription(
                        `${dmEmoji} **Seus produtos foram enviados por mensagem privada!**\n\n` +
                        `📦 **${deliveredProducts.length} produto(s) entregue(s)**\n` +
                        `✅ **Verifique suas mensagens privadas**`
                    )
                    .setTimestamp();

                await channel.send({ embeds: [confirmEmbed] });
                
            } catch (dmError) {
                logger.warn(`Falha ao enviar DM para ${cart.userId}, enviando no canal: ${dmError.message}`);
                
                // Se DM falhar, envia no canal (ticket)
                await this.sendProductsInChannel(channel, deliveredProducts, cart);
                logger.info(`Produtos entregues no canal para usuário ${cart.userId}`);
            }

        } catch (error) {
            logger.error('Erro na entrega automática de produtos:', error);
            
            // Notifica erro no canal
            const errorEmoji = await getEmoji(channel.guild.id, 'ERROR');
            const errorEmbed = new EmbedBuilder()
                .setColor(COLORS.ERROR)
                .setTitle(`${errorEmoji} Erro na Entrega`)
                .setDescription(
                    `${errorEmoji} **Ocorreu um erro na entrega automática dos produtos.**\n\n` +
                    `🔧 **Entre em contato com o suporte para receber seus produtos manualmente.**`
                )
                .setTimestamp();

            await channel.send({ embeds: [errorEmbed] });
        }
    }

    /**
     * Envia produtos por mensagem privada
     * @param {Object} user - Usuário do Discord
     * @param {Array} products - Lista de produtos entregues
     * @param {Object} cart - Carrinho de compras
     */
    static async sendProductsByDM(user, products, cart) {
        const deliveryEmoji = '📦';
        const successEmoji = '✅';
        
        let message = `${successEmoji} **Entrega de Produtos - ${cart.storeName}**\n\n`;
        message += `${deliveryEmoji} **Seus produtos foram entregues com sucesso!**\n\n`;
        
        for (const product of products) {
            message += `${product.emoji} **${product.name}** (${product.quantity}x)\n`;
            message += `\`\`\`\n`;
            
            for (let i = 0; i < product.content.length; i++) {
                message += `${i + 1}. ${product.content[i]}\n`;
            }
            
            message += `\`\`\`\n\n`;
        }
        
        message += `🛒 **Pedido:** \`${cart._id}\`\n`;
        message += `📅 **Data:** ${new Date().toLocaleString('pt-BR')}\n\n`;
        message += `⭐ **Obrigado pela sua compra!**`;
        
        // Divide a mensagem se for muito longa
        if (message.length > 2000) {
            const chunks = this.splitMessage(message, 2000);
            for (const chunk of chunks) {
                await user.send(chunk);
            }
        } else {
            await user.send(message);
        }
    }

    /**
     * Envia produtos no canal (quando DM falha)
     * @param {Object} channel - Canal do Discord
     * @param {Array} products - Lista de produtos entregues
     * @param {Object} cart - Carrinho de compras
     */
    static async sendProductsInChannel(channel, products, cart) {
        const deliveryEmoji = await getEmoji(channel.guild.id, 'PACKAGE');
        const warningEmoji = await getEmoji(channel.guild.id, 'WARNING');
        
        const embed = new EmbedBuilder()
            .setColor(COLORS.WARNING)
            .setTitle(`${deliveryEmoji} Produtos Entregues`)
            .setDescription(
                `${warningEmoji} **Não foi possível enviar por mensagem privada.**\n` +
                `${deliveryEmoji} **Seus produtos estão abaixo:**\n\n` +
                `⚠️ **ATENÇÃO: Salve estas informações em local seguro!**`
            )
            .setTimestamp();

        await channel.send({ embeds: [embed] });
        
        // Envia cada produto em mensagem separada
        for (const product of products) {
            let message = `${product.emoji} **${product.name}** (${product.quantity}x)\n\n`;
            
            for (let i = 0; i < product.content.length; i++) {
                message += `\`${i + 1}. ${product.content[i]}\`\n`;
            }
            
            // Divide a mensagem se for muito longa
            if (message.length > 2000) {
                const chunks = this.splitMessage(message, 2000);
                for (const chunk of chunks) {
                    await channel.send(chunk);
                }
            } else {
                await channel.send(message);
            }
        }
        
        // Mensagem final
        const finalEmbed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setDescription(
                `🛒 **Pedido:** \`${cart._id}\`\n` +
                `📅 **Data:** ${new Date().toLocaleString('pt-BR')}\n\n` +
                `⭐ **Obrigado pela sua compra!**`
            )
            .setTimestamp();

        await channel.send({ embeds: [finalEmbed] });
    }

    /**
     * Divide uma mensagem em chunks menores
     * @param {string} message - Mensagem para dividir
     * @param {number} maxLength - Tamanho máximo de cada chunk
     * @returns {Array} - Array de chunks
     */
    static splitMessage(message, maxLength = 2000) {
        const chunks = [];
        let currentChunk = '';
        
        const lines = message.split('\n');
        
        for (const line of lines) {
            if ((currentChunk + line + '\n').length > maxLength) {
                if (currentChunk) {
                    chunks.push(currentChunk.trim());
                    currentChunk = '';
                }
                
                // Se uma linha única for maior que o limite, força a divisão
                if (line.length > maxLength) {
                    const subChunks = line.match(new RegExp(`.{1,${maxLength}}`, 'g'));
                    chunks.push(...subChunks.slice(0, -1));
                    currentChunk = subChunks[subChunks.length - 1] + '\n';
                } else {
                    currentChunk = line + '\n';
                }
            } else {
                currentChunk += line + '\n';
            }
        }
        
        if (currentChunk.trim()) {
            chunks.push(currentChunk.trim());
        }
        
        return chunks;
    }
}

export default CartButtonHandler;
