import mongoose from 'mongoose';
import dotenv from 'dotenv';
import fs from 'fs/promises';
import path from 'path';
dotenv.config();

/**
 * Database Migration and Optimization Script
 * 
 * This script provides safe database migration with:
 * 1. Backup creation before changes
 * 2. Step-by-step migration process
 * 3. Rollback capabilities
 * 4. Data integrity verification
 */

class DatabaseMigration {
    constructor() {
        this.db = null;
        this.backupDir = './backups';
        this.migrationId = `migration_${Date.now()}`;
        this.dryRun = process.argv.includes('--dry-run');
        this.skipBackup = process.argv.includes('--skip-backup');
        this.verbose = process.argv.includes('--verbose');
    }

    async connect() {
        await mongoose.connect(process.env.MONGODB_URI);
        this.db = mongoose.connection.db;
        console.log('✅ Connected to MongoDB');
    }

    async disconnect() {
        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');
    }

    log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] [${level}] ${message}`);
    }

    async ensureBackupDirectory() {
        try {
            await fs.access(this.backupDir);
        } catch {
            await fs.mkdir(this.backupDir, { recursive: true });
            this.log(`Created backup directory: ${this.backupDir}`);
        }
    }

    /**
     * Create backup of collections before migration
     */
    async createBackup() {
        if (this.skipBackup) {
            this.log('Skipping backup creation');
            return null;
        }

        this.log('=== CREATING BACKUP ===');
        await this.ensureBackupDirectory();

        const backupPath = path.join(this.backupDir, `${this.migrationId}.json`);
        const backup = {
            migrationId: this.migrationId,
            timestamp: new Date().toISOString(),
            collections: {}
        };

        // Collections to backup (legacy ones that will be modified/deleted)
        const collectionsToBackup = [
            'lojas', 'compras', 'produtos', 'configuracoes', 
            'pagamentos', 'estoque', 'orders', 'users', 'stock_items'
        ];

        for (const collectionName of collectionsToBackup) {
            try {
                const exists = await this.db.listCollections({ name: collectionName }).hasNext();
                if (exists) {
                    const documents = await this.db.collection(collectionName).find({}).toArray();
                    const indexes = await this.db.collection(collectionName).indexes();
                    
                    backup.collections[collectionName] = {
                        documents,
                        indexes,
                        count: documents.length
                    };
                    
                    this.log(`Backed up ${collectionName}: ${documents.length} documents, ${indexes.length} indexes`);
                }
            } catch (error) {
                this.log(`Failed to backup ${collectionName}: ${error.message}`, 'ERROR');
            }
        }

        await fs.writeFile(backupPath, JSON.stringify(backup, null, 2));
        this.log(`Backup created: ${backupPath}`);
        
        return backupPath;
    }

    /**
     * Migrate legacy data to new collections
     */
    async migrateLegacyData() {
        this.log('=== MIGRATING LEGACY DATA ===');

        const migrations = [
            {
                from: 'lojas',
                to: 'stores',
                transform: (doc) => ({
                    name: doc.nome,
                    description: doc.descricao,
                    banner: doc.imagem,
                    color: doc.cor,
                    guildId: doc.guild_id,
                    channelId: doc.canal_id,
                    createdBy: doc.dono_id,
                    isActive: true,
                    createdAt: doc.criado_em || new Date(),
                    updatedAt: new Date(),
                    migrated: true,
                    originalId: doc._id
                })
            },
            {
                from: 'produtos',
                to: 'products',
                transform: (doc) => ({
                    name: doc.nome,
                    description: doc.descricao,
                    price: doc.preco,
                    images: doc.imagem ? [doc.imagem] : [],
                    storeId: doc.loja_id,
                    isActive: doc.ativo !== false,
                    stock: 0, // Will be updated from estoque
                    category: 'Geral',
                    createdAt: doc.criado_em || new Date(),
                    updatedAt: new Date(),
                    migrated: true,
                    originalId: doc._id
                })
            }
        ];

        for (const migration of migrations) {
            const fromExists = await this.db.listCollections({ name: migration.from }).hasNext();
            if (!fromExists) {
                this.log(`Source collection ${migration.from} does not exist, skipping`);
                continue;
            }

            const legacyDocs = await this.db.collection(migration.from).find({}).toArray();
            if (legacyDocs.length === 0) {
                this.log(`No documents to migrate from ${migration.from}`);
                continue;
            }

            this.log(`Migrating ${legacyDocs.length} documents from ${migration.from} to ${migration.to}`);

            if (!this.dryRun) {
                const transformedDocs = legacyDocs.map(migration.transform);
                
                try {
                    // Check if documents already exist (avoid duplicates)
                    const existingDocs = await this.db.collection(migration.to)
                        .find({ migrated: true }).toArray();
                    
                    const existingOriginalIds = new Set(
                        existingDocs.map(doc => doc.originalId?.toString())
                    );
                    
                    const newDocs = transformedDocs.filter(
                        doc => !existingOriginalIds.has(doc.originalId?.toString())
                    );
                    
                    if (newDocs.length > 0) {
                        await this.db.collection(migration.to).insertMany(newDocs);
                        this.log(`Inserted ${newDocs.length} new documents into ${migration.to}`);
                    } else {
                        this.log(`All documents from ${migration.from} already migrated`);
                    }
                } catch (error) {
                    this.log(`Failed to migrate ${migration.from}: ${error.message}`, 'ERROR');
                }
            } else {
                this.log(`[DRY RUN] Would migrate ${legacyDocs.length} documents from ${migration.from} to ${migration.to}`);
            }
        }
    }

    /**
     * Clean up legacy collections
     */
    async cleanupLegacyCollections() {
        this.log('=== CLEANING UP LEGACY COLLECTIONS ===');

        const legacyCollections = [
            'lojas', 'compras', 'produtos', 'configuracoes', 
            'pagamentos', 'estoque'
        ];

        for (const collectionName of legacyCollections) {
            const exists = await this.db.listCollections({ name: collectionName }).hasNext();
            if (!exists) {
                this.log(`Collection ${collectionName} does not exist`);
                continue;
            }

            const count = await this.db.collection(collectionName).countDocuments();
            
            if (this.dryRun) {
                this.log(`[DRY RUN] Would drop collection: ${collectionName} (${count} documents)`);
            } else {
                try {
                    await this.db.collection(collectionName).drop();
                    this.log(`Dropped legacy collection: ${collectionName} (${count} documents)`);
                } catch (error) {
                    this.log(`Failed to drop ${collectionName}: ${error.message}`, 'ERROR');
                }
            }
        }
    }

    /**
     * Optimize indexes for better performance
     */
    async optimizeIndexes() {
        this.log('=== OPTIMIZING INDEXES ===');

        const indexOptimizations = [
            {
                collection: 'orders',
                dropIndexes: ['userId_1', 'productId_1', 'status_1', 'createdAt_1', 'guildId_1', 'paymentMethod_1', 'createdAt_-1'],
                createIndexes: [
                    { keys: { guildId: 1, status: 1, createdAt: -1 }, options: { name: 'guild_status_date_optimized' } }
                ]
            },
            {
                collection: 'users',
                dropIndexes: ['createdAt_-1', 'vipLevel_1', 'lastActivity_-1', 'totalOrders_-1', 'status_1'],
                createIndexes: [
                    { keys: { discordId: 1, isBlacklisted: 1 }, options: { name: 'discord_blacklist_check' } }
                ]
            },
            {
                collection: 'stock_items',
                dropIndexes: ['productId_1', 'storeId_1', 'status_1', 'soldTo_1_soldAt_-1', 'reservedTo_1_reservationExpires_1'],
                createIndexes: [
                    { keys: { productId: 1, status: 1, createdAt: -1 }, options: { name: 'product_status_date' } }
                ]
            },
            {
                collection: 'products',
                createIndexes: [
                    { keys: { storeId: 1, isActive: 1, featured: -1 }, options: { name: 'store_active_featured' } },
                    { keys: { category: 1, price: 1 }, options: { name: 'category_price_filter' } }
                ]
            }
        ];

        for (const optimization of indexOptimizations) {
            const exists = await this.db.listCollections({ name: optimization.collection }).hasNext();
            if (!exists) {
                this.log(`Collection ${optimization.collection} does not exist, skipping`);
                continue;
            }

            // Drop redundant indexes
            if (optimization.dropIndexes) {
                const currentIndexes = await this.db.collection(optimization.collection).indexes();
                const currentIndexNames = currentIndexes.map(idx => idx.name);

                for (const indexName of optimization.dropIndexes) {
                    if (currentIndexNames.includes(indexName)) {
                        if (this.dryRun) {
                            this.log(`[DRY RUN] Would drop index: ${optimization.collection}.${indexName}`);
                        } else {
                            try {
                                await this.db.collection(optimization.collection).dropIndex(indexName);
                                this.log(`Dropped redundant index: ${optimization.collection}.${indexName}`);
                            } catch (error) {
                                this.log(`Failed to drop index ${indexName}: ${error.message}`, 'ERROR');
                            }
                        }
                    }
                }
            }

            // Create new optimized indexes
            if (optimization.createIndexes) {
                for (const indexData of optimization.createIndexes) {
                    if (this.dryRun) {
                        this.log(`[DRY RUN] Would create index: ${optimization.collection}.${indexData.options.name}`);
                    } else {
                        try {
                            await this.db.collection(optimization.collection).createIndex(indexData.keys, indexData.options);
                            this.log(`Created optimized index: ${optimization.collection}.${indexData.options.name}`);
                        } catch (error) {
                            if (error.code === 85) {
                                this.log(`Index already exists: ${optimization.collection}.${indexData.options.name}`);
                            } else {
                                this.log(`Failed to create index ${indexData.options.name}: ${error.message}`, 'ERROR');
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Verify data integrity after migration
     */
    async verifyIntegrity() {
        this.log('=== VERIFYING DATA INTEGRITY ===');

        const checks = [
            {
                name: 'Products have valid store references',
                check: async () => {
                    const { default: Product } = await import('../src/models/Product.js');
                    const { default: Store } = await import('../src/models/Store.js');
                    
                    const products = await Product.find({}).lean();
                    const storeIds = new Set((await Store.find({}).lean()).map(s => s._id.toString()));
                    
                    const orphanedProducts = products.filter(p => p.storeId && !storeIds.has(p.storeId.toString()));
                    return {
                        passed: orphanedProducts.length === 0,
                        details: `Found ${orphanedProducts.length} orphaned products`
                    };
                }
            },
            {
                name: 'Stock items have valid product references',
                check: async () => {
                    const { default: StockItem } = await import('../src/models/StockItem.js');
                    const { default: Product } = await import('../src/models/Product.js');
                    
                    const stockItems = await StockItem.find({}).lean();
                    const productIds = new Set((await Product.find({}).lean()).map(p => p._id.toString()));
                    
                    const orphanedStock = stockItems.filter(s => s.productId && !productIds.has(s.productId.toString()));
                    return {
                        passed: orphanedStock.length === 0,
                        details: `Found ${orphanedStock.length} orphaned stock items`
                    };
                }
            }
        ];

        let allPassed = true;
        for (const check of checks) {
            try {
                const result = await check.check();
                if (result.passed) {
                    this.log(`✅ ${check.name}: ${result.details}`);
                } else {
                    this.log(`❌ ${check.name}: ${result.details}`, 'ERROR');
                    allPassed = false;
                }
            } catch (error) {
                this.log(`❌ ${check.name}: Failed - ${error.message}`, 'ERROR');
                allPassed = false;
            }
        }

        return allPassed;
    }

    /**
     * Generate migration report
     */
    async generateReport() {
        this.log('=== GENERATING MIGRATION REPORT ===');

        const collections = await this.db.listCollections().toArray();
        let totalDocuments = 0;
        let totalIndexes = 0;

        const report = {
            migrationId: this.migrationId,
            timestamp: new Date().toISOString(),
            dryRun: this.dryRun,
            collections: [],
            summary: {}
        };

        for (const collection of collections) {
            const count = await this.db.collection(collection.name).countDocuments();
            const indexes = await this.db.collection(collection.name).indexes();
            totalDocuments += count;
            totalIndexes += indexes.length;

            const collectionInfo = {
                name: collection.name,
                documents: count,
                indexes: indexes.length,
                indexNames: indexes.map(idx => idx.name)
            };

            report.collections.push(collectionInfo);

            if (this.verbose) {
                this.log(`${collection.name}: ${count} docs, ${indexes.length} indexes`);
            }
        }

        report.summary = {
            totalCollections: collections.length,
            totalDocuments,
            totalIndexes
        };

        this.log(`Total collections: ${collections.length}`);
        this.log(`Total documents: ${totalDocuments}`);
        this.log(`Total indexes: ${totalIndexes}`);

        // Save report
        const reportPath = path.join(this.backupDir, `${this.migrationId}_report.json`);
        await this.ensureBackupDirectory();
        await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
        this.log(`Migration report saved: ${reportPath}`);

        return report;
    }

    /**
     * Run complete migration process
     */
    async run() {
        try {
            await this.connect();

            this.log('Starting database migration...');
            if (this.dryRun) {
                this.log('Running in DRY RUN mode - no changes will be made');
            }

            // Step 1: Create backup
            const backupPath = await this.createBackup();

            // Step 2: Migrate legacy data
            await this.migrateLegacyData();

            // Step 3: Optimize indexes
            await this.optimizeIndexes();

            // Step 4: Clean up legacy collections (only after successful migration)
            if (!this.dryRun) {
                await this.cleanupLegacyCollections();
            }

            // Step 5: Verify data integrity
            const integrityPassed = await this.verifyIntegrity();

            // Step 6: Generate report
            await this.generateReport();

            if (integrityPassed) {
                this.log('✅ Database migration completed successfully!');
            } else {
                this.log('⚠️ Database migration completed with integrity warnings', 'WARN');
            }

            if (backupPath) {
                this.log(`💾 Backup available at: ${backupPath}`);
            }

        } catch (error) {
            this.log(`❌ Database migration failed: ${error.message}`, 'ERROR');
            throw error;
        } finally {
            await this.disconnect();
        }
    }
}

// Run the migration
const migration = new DatabaseMigration();
migration.run().catch(console.error);