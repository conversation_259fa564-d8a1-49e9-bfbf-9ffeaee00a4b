import { logger } from './logger.js';
import { cache } from './cacheManager.js';

/**
 * Database Query Optimization Utility
 * 
 * Provides optimized queries for:
 * - Complex aggregation pipelines
 * - Multi-collection joins
 * - Search operations across large datasets
 * - Performance monitoring and metrics
 */

class QueryOptimizer {
    constructor() {
        this.queryMetrics = new Map();
        this.slowQueryThreshold = 1000; // 1 second
        this.enableMetrics = process.env.NODE_ENV !== 'production';
    }
    
    /**
     * Execute query with performance monitoring
     * @param {string} queryName - Name for tracking
     * @param {Function} queryFunction - Query function to execute
     * @param {Object} options - Options for caching and optimization
     * @returns {any} Query result
     */
    async executeQuery(queryName, queryFunction, options = {}) {
        const startTime = Date.now();
        const cacheKey = options.cacheKey;
        const cacheTTL = options.cacheTTL;
        const cacheType = options.cacheType || 'products';
        
        try {
            // Try cache first if enabled
            if (cacheKey) {
                const cachedResult = cache.get(cacheType, cacheKey);
                if (cachedResult !== null) {
                    this.recordMetric(queryName, Date.now() - startTime, true);
                    return cachedResult;
                }
            }
            
            // Execute query
            const result = await queryFunction();
            const executionTime = Date.now() - startTime;
            
            // Cache result if specified
            if (cacheKey && result) {
                cache.set(cacheType, cacheKey, result, cacheTTL);
            }
            
            // Record metrics
            this.recordMetric(queryName, executionTime, false);
            
            // Log slow queries
            if (executionTime > this.slowQueryThreshold) {
                logger.warn(`Slow query detected: ${queryName} took ${executionTime}ms`);
            }
            
            return result;
        } catch (error) {
            const executionTime = Date.now() - startTime;
            this.recordMetric(queryName, executionTime, false, true);
            logger.error(`Query failed: ${queryName}`, error);
            throw error;
        }
    }
    
    /**
     * Record query performance metrics
     */
    recordMetric(queryName, executionTime, fromCache, isError = false) {
        if (!this.enableMetrics) return;
        
        if (!this.queryMetrics.has(queryName)) {
            this.queryMetrics.set(queryName, {
                totalExecutions: 0,
                totalTime: 0,
                cacheHits: 0,
                errors: 0,
                avgTime: 0,
                maxTime: 0,
                minTime: Infinity
            });
        }
        
        const metrics = this.queryMetrics.get(queryName);
        metrics.totalExecutions++;
        
        if (fromCache) {
            metrics.cacheHits++;
        } else {
            metrics.totalTime += executionTime;
            metrics.maxTime = Math.max(metrics.maxTime, executionTime);
            metrics.minTime = Math.min(metrics.minTime, executionTime);
            metrics.avgTime = metrics.totalTime / (metrics.totalExecutions - metrics.cacheHits);
        }
        
        if (isError) {
            metrics.errors++;
        }
    }
    
    /**
     * Optimized product search with aggregation
     * @param {Object} filters - Search filters
     * @param {Object} options - Query options
     * @returns {Array} Search results
     */
    async searchProducts(filters = {}, options = {}) {
        const {
            storeId,
            category,
            priceMin,
            priceMax,
            searchText,
            isActive = true,
            sortBy = 'createdAt',
            sortOrder = -1,
            limit = 20,
            skip = 0
        } = filters;
        
        const cacheKey = `search:${JSON.stringify(filters)}`;
        
        return this.executeQuery('searchProducts', async () => {
            const { default: Product } = await import('../models/Product.js');
            
            const pipeline = [];
            
            // Match stage
            const matchStage = { isActive };
            
            if (storeId) matchStage.storeId = storeId;
            if (category) matchStage.category = category;
            if (priceMin !== undefined || priceMax !== undefined) {
                matchStage.price = {};
                if (priceMin !== undefined) matchStage.price.$gte = priceMin;
                if (priceMax !== undefined) matchStage.price.$lte = priceMax;
            }
            
            if (searchText) {
                matchStage.$or = [
                    { name: { $regex: searchText, $options: 'i' } },
                    { description: { $regex: searchText, $options: 'i' } },
                    { tags: { $in: [new RegExp(searchText, 'i')] } }
                ];
            }
            
            pipeline.push({ $match: matchStage });
            
            // Lookup store information
            pipeline.push({
                $lookup: {
                    from: 'stores',
                    localField: 'storeId',
                    foreignField: '_id',
                    as: 'store'
                }
            });
            
            // Unwind store (optional)
            pipeline.push({
                $unwind: {
                    path: '$store',
                    preserveNullAndEmptyArrays: true
                }
            });
            
            // Add computed fields
            pipeline.push({
                $addFields: {
                    hasStock: { $gt: ['$stock', 0] },
                    discountPercentage: {
                        $cond: {
                            if: { $and: ['$originalPrice', { $gt: ['$originalPrice', '$price'] }] },
                            then: {
                                $multiply: [
                                    { $divide: [{ $subtract: ['$originalPrice', '$price'] }, '$originalPrice'] },
                                    100
                                ]
                            },
                            else: 0
                        }
                    }
                }
            });
            
            // Sort stage
            const sortStage = {};
            sortStage[sortBy] = sortOrder;
            pipeline.push({ $sort: sortStage });
            
            // Pagination
            if (skip > 0) pipeline.push({ $skip: skip });
            if (limit > 0) pipeline.push({ $limit: limit });
            
            // Project final fields
            pipeline.push({
                $project: {
                    name: 1,
                    description: 1,
                    price: 1,
                    originalPrice: 1,
                    stock: 1,
                    images: 1,
                    category: 1,
                    tags: 1,
                    isActive: 1,
                    featured: 1,
                    totalSold: 1,
                    views: 1,
                    hasStock: 1,
                    discountPercentage: 1,
                    'store.name': 1,
                    'store.color': 1,
                    createdAt: 1,
                    updatedAt: 1
                }
            });
            
            return await Product.aggregate(pipeline);
        }, {
            cacheKey,
            cacheTTL: 300, // 5 minutes
            cacheType: 'products'
        });
    }
    
    /**
     * Optimized order analytics aggregation
     * @param {Object} filters - Analytics filters
     * @returns {Object} Analytics data
     */
    async getOrderAnalytics(filters = {}) {
        const {
            storeId,
            startDate,
            endDate,
            groupBy = 'day'
        } = filters;
        
        const cacheKey = `analytics:orders:${JSON.stringify(filters)}`;
        
        return this.executeQuery('getOrderAnalytics', async () => {
            const { default: Order } = await import('../models/Order.js');
            
            const pipeline = [];
            
            // Match stage
            const matchStage = { status: { $ne: 'cancelled' } };
            
            if (storeId) {
                matchStage['items.storeId'] = storeId;
            }
            
            if (startDate || endDate) {
                matchStage.createdAt = {};
                if (startDate) matchStage.createdAt.$gte = new Date(startDate);
                if (endDate) matchStage.createdAt.$lte = new Date(endDate);
            }
            
            pipeline.push({ $match: matchStage });
            
            // Group by time period
            let groupId;
            switch (groupBy) {
                case 'hour':
                    groupId = {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                        day: { $dayOfMonth: '$createdAt' },
                        hour: { $hour: '$createdAt' }
                    };
                    break;
                case 'day':
                    groupId = {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                        day: { $dayOfMonth: '$createdAt' }
                    };
                    break;
                case 'month':
                    groupId = {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' }
                    };
                    break;
                default:
                    groupId = {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                        day: { $dayOfMonth: '$createdAt' }
                    };
            }
            
            pipeline.push({
                $group: {
                    _id: groupId,
                    totalOrders: { $sum: 1 },
                    totalRevenue: { $sum: '$total' },
                    avgOrderValue: { $avg: '$total' },
                    uniqueCustomers: { $addToSet: '$userId' },
                    completedOrders: {
                        $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
                    },
                    pendingOrders: {
                        $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
                    }
                }
            });
            
            // Add computed fields
            pipeline.push({
                $addFields: {
                    uniqueCustomerCount: { $size: '$uniqueCustomers' },
                    completionRate: {
                        $multiply: [
                            { $divide: ['$completedOrders', '$totalOrders'] },
                            100
                        ]
                    }
                }
            });
            
            // Sort by date
            pipeline.push({ $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.hour': 1 } });
            
            return await Order.aggregate(pipeline);
        }, {
            cacheKey,
            cacheTTL: 600, // 10 minutes
            cacheType: 'analytics'
        });
    }
    
    /**
     * Optimized stock availability check
     * @param {Array} productIds - Product IDs to check
     * @returns {Object} Stock availability map
     */
    async checkStockAvailability(productIds) {
        const cacheKey = `stock:availability:${productIds.sort().join(',')}`;
        
        return this.executeQuery('checkStockAvailability', async () => {
            const { default: StockItem } = await import('../models/StockItem.js');
            
            const pipeline = [
                {
                    $match: {
                        productId: { $in: productIds },
                        status: 'available'
                    }
                },
                {
                    $group: {
                        _id: '$productId',
                        availableCount: { $sum: 1 },
                        items: { $push: '$$ROOT' }
                    }
                },
                {
                    $project: {
                        productId: '$_id',
                        availableCount: 1,
                        hasStock: { $gt: ['$availableCount', 0] },
                        items: { $slice: ['$items', 5] } // Limit to first 5 items
                    }
                }
            ];
            
            const results = await StockItem.aggregate(pipeline);
            
            // Convert to map for easy lookup
            const stockMap = {};
            productIds.forEach(id => {
                stockMap[id] = { availableCount: 0, hasStock: false, items: [] };
            });
            
            results.forEach(result => {
                stockMap[result.productId] = {
                    availableCount: result.availableCount,
                    hasStock: result.hasStock,
                    items: result.items
                };
            });
            
            return stockMap;
        }, {
            cacheKey,
            cacheTTL: 60, // 1 minute
            cacheType: 'stock'
        });
    }
    
    /**
     * Optimized user transaction history
     * @param {string} userId - User ID
     * @param {Object} options - Query options
     * @returns {Array} Transaction history
     */
    async getUserTransactionHistory(userId, options = {}) {
        const {
            limit = 50,
            skip = 0,
            startDate,
            endDate,
            status
        } = options;
        
        const cacheKey = `user:${userId}:transactions:${JSON.stringify(options)}`;
        
        return this.executeQuery('getUserTransactionHistory', async () => {
            const { default: Order } = await import('../models/Order.js');
            
            const pipeline = [];
            
            // Match stage
            const matchStage = { userId };
            
            if (status) matchStage.status = status;
            if (startDate || endDate) {
                matchStage.createdAt = {};
                if (startDate) matchStage.createdAt.$gte = new Date(startDate);
                if (endDate) matchStage.createdAt.$lte = new Date(endDate);
            }
            
            pipeline.push({ $match: matchStage });
            
            // Lookup product details for items
            pipeline.push({
                $lookup: {
                    from: 'products',
                    localField: 'items.productId',
                    foreignField: '_id',
                    as: 'productDetails'
                }
            });
            
            // Add computed fields
            pipeline.push({
                $addFields: {
                    itemCount: { $size: '$items' },
                    daysSinceOrder: {
                        $divide: [
                            { $subtract: [new Date(), '$createdAt'] },
                            1000 * 60 * 60 * 24
                        ]
                    }
                }
            });
            
            // Sort by creation date (newest first)
            pipeline.push({ $sort: { createdAt: -1 } });
            
            // Pagination
            if (skip > 0) pipeline.push({ $skip: skip });
            if (limit > 0) pipeline.push({ $limit: limit });
            
            // Project final fields
            pipeline.push({
                $project: {
                    orderNumber: 1,
                    status: 1,
                    total: 1,
                    itemCount: 1,
                    paymentMethod: 1,
                    createdAt: 1,
                    completedAt: 1,
                    daysSinceOrder: 1,
                    'productDetails.name': 1,
                    'productDetails.images': 1
                }
            });
            
            return await Order.aggregate(pipeline);
        }, {
            cacheKey,
            cacheTTL: 180, // 3 minutes
            cacheType: 'users'
        });
    }
    
    /**
     * Get query performance metrics
     * @returns {Object} Performance metrics
     */
    getMetrics() {
        const metrics = {};
        
        for (const [queryName, data] of this.queryMetrics.entries()) {
            metrics[queryName] = {
                ...data,
                cacheHitRate: data.totalExecutions > 0 
                    ? (data.cacheHits / data.totalExecutions * 100).toFixed(2) + '%'
                    : '0%',
                errorRate: data.totalExecutions > 0
                    ? (data.errors / data.totalExecutions * 100).toFixed(2) + '%'
                    : '0%'
            };
        }
        
        return metrics;
    }
    
    /**
     * Reset metrics
     */
    resetMetrics() {
        this.queryMetrics.clear();
        logger.info('Query metrics reset');
    }
    
    /**
     * Log performance summary
     */
    logPerformanceSummary() {
        const metrics = this.getMetrics();
        const queryNames = Object.keys(metrics);
        
        if (queryNames.length === 0) {
            logger.info('No query metrics available');
            return;
        }
        
        logger.info('Query Performance Summary:', {
            totalQueries: queryNames.length,
            metrics: Object.fromEntries(
                Object.entries(metrics)
                    .sort(([,a], [,b]) => b.totalExecutions - a.totalExecutions)
                    .slice(0, 10) // Top 10 most used queries
            )
        });
    }
}

// Create singleton instance
const queryOptimizer = new QueryOptimizer();

// Export helper functions
export const optimizedQueries = {
    searchProducts: (filters, options) => queryOptimizer.searchProducts(filters, options),
    getOrderAnalytics: (filters) => queryOptimizer.getOrderAnalytics(filters),
    checkStockAvailability: (productIds) => queryOptimizer.checkStockAvailability(productIds),
    getUserTransactionHistory: (userId, options) => queryOptimizer.getUserTransactionHistory(userId, options),
    executeQuery: (name, queryFn, options) => queryOptimizer.executeQuery(name, queryFn, options),
    getMetrics: () => queryOptimizer.getMetrics(),
    resetMetrics: () => queryOptimizer.resetMetrics(),
    logSummary: () => queryOptimizer.logPerformanceSummary()
};

export default queryOptimizer;