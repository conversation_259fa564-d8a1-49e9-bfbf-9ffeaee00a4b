import { SlashCommandBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder, ActionRowBuilder } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import Store from '../../models/Store.js';

export default {
    data: new SlashCommandBuilder()
        .setName('editar-loja')
        .setDescription('Edita uma loja existente no servidor (apenas administradores)'),
    
    async execute(interaction) {
        try {
            // Verificação se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem executar este comando.',
                    ephemeral: true
                });
            }

            // Busca lojas ativas no servidor atual
            const stores = await Store.findByGuild(interaction.guild.id);

            if (stores.length === 0) {
                return await interaction.reply({
                    content: '❌ Não há lojas criadas neste servidor. Use `/criar-loja` para criar uma nova loja.',
                    ephemeral: true
                });
            }

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('edit_store_select')
                .setPlaceholder('Selecione uma loja para editar')
                .addOptions(
                    stores.map(store => 
                        new StringSelectMenuOptionBuilder()
                            .setLabel(store.name)
                            .setDescription(`Canal: #${store.channelName || 'canal-removido'}`)
                            .setValue(store._id.toString())
                    )
                )
                .addOptions(
                    new StringSelectMenuOptionBuilder()
                        .setLabel('❌ Cancelar Seleção')
                        .setDescription('Cancelar a seleção')
                        .setValue('cancel')
                );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.reply({
                content: 'Selecione uma loja para editar:',
                components: [row],
                ephemeral: true
            });

            logger.info(`Dropdown de edição de loja exibido para ${interaction.user.tag} em ${interaction.guild.name}`);

        } catch (error) {
            logger.error('Erro ao executar comando editar-loja:', error);
            
            const errorMessage = {
                content: '❌ Erro interno do servidor. Tente novamente mais tarde.',
                ephemeral: true
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    }
};
