import mongoose from 'mongoose';
import dotenv from 'dotenv';
dotenv.config();

/**
 * Database Cleanup and Optimization Script
 * 
 * This script performs:
 * 1. Identifies legacy/unused collections
 * 2. Removes orphaned indexes
 * 3. Optimizes collection schemas
 * 4. Implements data retention policies
 */

class DatabaseOptimizer {
    constructor() {
        this.db = null;
        this.dryRun = process.argv.includes('--dry-run');
        this.verbose = process.argv.includes('--verbose');
    }

    async connect() {
        await mongoose.connect(process.env.MONGODB_URI);
        this.db = mongoose.connection.db;
        console.log('Connected to MongoDB');
    }

    async disconnect() {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
    }

    log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] [${level}] ${message}`);
    }

    async identifyLegacyCollections() {
        this.log('=== IDENTIFYING LEGACY COLLECTIONS ===');
        
        const collections = await this.db.listCollections().toArray();
        
        // Define current active collections based on models
        const activeCollections = [
            'products', 'stores', 'orders', 'users', 'stock_items',
            'bot_configs', 'emoji_configs', 'permissions', 'analytics', 'coupons'
        ];
        
        // Portuguese legacy collections (old system)
        const legacyCollections = [
            'lojas', 'compras', 'produtos', 'configuracoes'
        ];
        
        // Collections that might be unused
        const potentiallyUnused = [];
        const definitelyLegacy = [];
        
        for (const collection of collections) {
            const name = collection.name;
            const count = await this.db.collection(name).countDocuments();
            
            if (legacyCollections.includes(name)) {
                definitelyLegacy.push({ name, count });
            } else if (!activeCollections.includes(name) && name !== 'configs') {
                potentiallyUnused.push({ name, count });
            }
        }
        
        this.log(`Found ${definitelyLegacy.length} legacy collections`);
        definitelyLegacy.forEach(col => {
            this.log(`  - ${col.name} (${col.count} documents)`);
        });
        
        if (potentiallyUnused.length > 0) {
            this.log(`Found ${potentiallyUnused.length} potentially unused collections`);
            potentiallyUnused.forEach(col => {
                this.log(`  - ${col.name} (${col.count} documents)`);
            });
        }
        
        return { definitelyLegacy, potentiallyUnused };
    }

    async cleanupLegacyCollections(legacyCollections) {
        this.log('=== CLEANING UP LEGACY COLLECTIONS ===');
        
        for (const collection of legacyCollections) {
            if (this.dryRun) {
                this.log(`[DRY RUN] Would drop collection: ${collection.name}`);
            } else {
                try {
                    await this.db.collection(collection.name).drop();
                    this.log(`Dropped legacy collection: ${collection.name}`);
                } catch (error) {
                    this.log(`Failed to drop ${collection.name}: ${error.message}`, 'ERROR');
                }
            }
        }
    }

    async optimizeIndexes() {
        this.log('=== OPTIMIZING INDEXES ===');
        
        const collectionsToOptimize = [
            {
                name: 'orders',
                // Remove redundant indexes, keep essential ones
                keepIndexes: ['_id_', 'orderId_1', 'userId_1_status_1', 'status_1_createdAt_-1'],
                removeIndexes: ['userId_1', 'productId_1', 'status_1', 'createdAt_1', 'guildId_1', 'paymentMethod_1', 'createdAt_-1']
            },
            {
                name: 'users',
                keepIndexes: ['_id_', 'userId_1', 'discordId_1', 'isBlacklisted_1', 'totalSpent_-1'],
                removeIndexes: ['createdAt_-1', 'vipLevel_1', 'lastActivity_-1', 'totalOrders_-1', 'status_1']
            },
            {
                name: 'stock_items',
                keepIndexes: ['_id_', 'productId_1_status_1', 'storeId_1_status_1', 'productId_1_storeId_1_status_1'],
                removeIndexes: ['productId_1', 'storeId_1', 'status_1', 'soldTo_1_soldAt_-1', 'reservedTo_1_reservationExpires_1']
            }
        ];
        
        for (const collection of collectionsToOptimize) {
            const exists = await this.db.listCollections({ name: collection.name }).hasNext();
            if (!exists) {
                this.log(`Collection ${collection.name} does not exist, skipping`);
                continue;
            }
            
            const currentIndexes = await this.db.collection(collection.name).indexes();
            const currentIndexNames = currentIndexes.map(idx => idx.name);
            
            this.log(`Optimizing indexes for ${collection.name}`);
            
            for (const indexName of collection.removeIndexes) {
                if (currentIndexNames.includes(indexName)) {
                    if (this.dryRun) {
                        this.log(`[DRY RUN] Would drop index: ${collection.name}.${indexName}`);
                    } else {
                        try {
                            await this.db.collection(collection.name).dropIndex(indexName);
                            this.log(`Dropped redundant index: ${collection.name}.${indexName}`);
                        } catch (error) {
                            this.log(`Failed to drop index ${indexName}: ${error.message}`, 'ERROR');
                        }
                    }
                }
            }
        }
    }

    async implementDataRetention() {
        this.log('=== IMPLEMENTING DATA RETENTION ===');
        
        const retentionPolicies = [
            {
                collection: 'analytics',
                field: 'createdAt',
                retentionDays: 90,
                description: 'Analytics data older than 90 days'
            },
            {
                collection: 'orders',
                field: 'createdAt',
                retentionDays: 365,
                description: 'Completed orders older than 1 year',
                additionalFilter: { status: 'completed' }
            }
        ];
        
        for (const policy of retentionPolicies) {
            const exists = await this.db.listCollections({ name: policy.collection }).hasNext();
            if (!exists) {
                this.log(`Collection ${policy.collection} does not exist, skipping retention policy`);
                continue;
            }
            
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - policy.retentionDays);
            
            const filter = {
                [policy.field]: { $lt: cutoffDate },
                ...policy.additionalFilter
            };
            
            const count = await this.db.collection(policy.collection).countDocuments(filter);
            
            if (count > 0) {
                if (this.dryRun) {
                    this.log(`[DRY RUN] Would delete ${count} documents: ${policy.description}`);
                } else {
                    const result = await this.db.collection(policy.collection).deleteMany(filter);
                    this.log(`Deleted ${result.deletedCount} documents: ${policy.description}`);
                }
            } else {
                this.log(`No documents to delete for: ${policy.description}`);
            }
        }
    }

    async createOptimalIndexes() {
        this.log('=== CREATING OPTIMAL INDEXES ===');
        
        const newIndexes = [
            {
                collection: 'products',
                indexes: [
                    { keys: { storeId: 1, isActive: 1, featured: -1 }, options: { name: 'store_active_featured' } },
                    { keys: { category: 1, price: 1 }, options: { name: 'category_price' } }
                ]
            },
            {
                collection: 'orders',
                indexes: [
                    { keys: { guildId: 1, status: 1, createdAt: -1 }, options: { name: 'guild_status_date' } }
                ]
            }
        ];
        
        for (const collectionData of newIndexes) {
            const exists = await this.db.listCollections({ name: collectionData.collection }).hasNext();
            if (!exists) {
                this.log(`Collection ${collectionData.collection} does not exist, skipping index creation`);
                continue;
            }
            
            for (const indexData of collectionData.indexes) {
                if (this.dryRun) {
                    this.log(`[DRY RUN] Would create index: ${collectionData.collection}.${indexData.options.name}`);
                } else {
                    try {
                        await this.db.collection(collectionData.collection).createIndex(indexData.keys, indexData.options);
                        this.log(`Created optimal index: ${collectionData.collection}.${indexData.options.name}`);
                    } catch (error) {
                        if (error.code === 85) { // Index already exists
                            this.log(`Index already exists: ${collectionData.collection}.${indexData.options.name}`);
                        } else {
                            this.log(`Failed to create index ${indexData.options.name}: ${error.message}`, 'ERROR');
                        }
                    }
                }
            }
        }
    }

    async generateReport() {
        this.log('=== GENERATING OPTIMIZATION REPORT ===');
        
        const collections = await this.db.listCollections().toArray();
        let totalDocuments = 0;
        let totalIndexes = 0;
        
        for (const collection of collections) {
            const count = await this.db.collection(collection.name).countDocuments();
            const indexes = await this.db.collection(collection.name).indexes();
            totalDocuments += count;
            totalIndexes += indexes.length;
            
            if (this.verbose) {
                this.log(`${collection.name}: ${count} docs, ${indexes.length} indexes`);
            }
        }
        
        this.log(`Total collections: ${collections.length}`);
        this.log(`Total documents: ${totalDocuments}`);
        this.log(`Total indexes: ${totalIndexes}`);
    }

    async run() {
        try {
            await this.connect();
            
            this.log('Starting database optimization...');
            if (this.dryRun) {
                this.log('Running in DRY RUN mode - no changes will be made');
            }
            
            // Step 1: Identify legacy collections
            const { definitelyLegacy } = await this.identifyLegacyCollections();
            
            // Step 2: Clean up legacy collections
            if (definitelyLegacy.length > 0) {
                await this.cleanupLegacyCollections(definitelyLegacy);
            }
            
            // Step 3: Optimize indexes
            await this.optimizeIndexes();
            
            // Step 4: Create optimal indexes
            await this.createOptimalIndexes();
            
            // Step 5: Implement data retention
            await this.implementDataRetention();
            
            // Step 6: Generate final report
            await this.generateReport();
            
            this.log('Database optimization completed successfully!');
            
        } catch (error) {
            this.log(`Database optimization failed: ${error.message}`, 'ERROR');
            throw error;
        } finally {
            await this.disconnect();
        }
    }
}

// Run the optimizer
const optimizer = new DatabaseOptimizer();
optimizer.run().catch(console.error);