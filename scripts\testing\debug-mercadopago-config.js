import dotenv from 'dotenv';
import mongoose from 'mongoose';
import BotConfig from '../src/models/BotConfig.js';
import { logger } from '../src/utils/logger.js';

// Carrega variáveis de ambiente
dotenv.config();

/**
 * Script para debugar configurações do MercadoPago no banco de dados
 */
async function debugMercadoPagoConfig() {
    try {
        console.log('🔄 Conectando ao MongoDB...');
        
        if (!process.env.MONGODB_URI) {
            throw new Error('MONGODB_URI não está definida no arquivo .env');
        }

        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB');

        // Lista todas as configurações
        const allConfigs = await BotConfig.find({});
        console.log(`\n📋 Total de configurações encontradas: ${allConfigs.length}`);

        if (allConfigs.length === 0) {
            console.log('❌ Nenhuma configuração encontrada no banco de dados');
            return;
        }

        // Analisa cada configuração
        for (const config of allConfigs) {
            console.log(`\n🏪 Guild ID: ${config.guildId}`);
            console.log(`📅 Criado em: ${config.createdAt}`);
            console.log(`📝 Última modificação: ${config.updatedAt}`);
            console.log(`👤 Modificado por: ${config.lastModifiedBy || 'N/A'}`);
            
            // Verifica configuração do MercadoPago
            if (config.mercadoPago) {
                console.log('💳 Configuração MercadoPago:');
                console.log(`   ✅ Habilitado: ${config.mercadoPago.isEnabled ? 'Sim' : 'Não'}`);
                
                if (config.mercadoPago.accessToken) {
                    const tokenStart = config.mercadoPago.accessToken.substring(0, 20);
                    const tokenType = config.mercadoPago.accessToken.startsWith('TEST-') ? 'Sandbox' : 
                                     config.mercadoPago.accessToken.startsWith('APP_USR-') ? 'Produção' : 'Desconhecido';
                    console.log(`   🔑 Access Token: ${tokenStart}... (${tokenType})`);
                } else {
                    console.log('   ❌ Access Token: Não configurado');
                }
                
                if (config.mercadoPago.publicKey) {
                    const keyStart = config.mercadoPago.publicKey.substring(0, 20);
                    const keyType = config.mercadoPago.publicKey.startsWith('TEST-') ? 'Sandbox' : 
                                   config.mercadoPago.publicKey.startsWith('APP_USR-') ? 'Produção' : 'Desconhecido';
                    console.log(`   🔓 Public Key: ${keyStart}... (${keyType})`);
                } else {
                    console.log('   ❌ Public Key: Não configurado');
                }
                
                if (config.mercadoPago.webhookSecret) {
                    console.log(`   🔗 Webhook Secret: ${config.mercadoPago.webhookSecret.substring(0, 10)}...`);
                } else {
                    console.log('   ⚠️ Webhook Secret: Não configurado');
                }
                
                // Verifica consistência dos tokens
                if (config.mercadoPago.accessToken && config.mercadoPago.publicKey) {
                    const accessTokenEnv = config.mercadoPago.accessToken.startsWith('TEST-') ? 'sandbox' : 'production';
                    const publicKeyEnv = config.mercadoPago.publicKey.startsWith('TEST-') ? 'sandbox' : 'production';
                    
                    if (accessTokenEnv === publicKeyEnv) {
                        console.log(`   ✅ Consistência: Ambos tokens são do ambiente ${accessTokenEnv}`);
                    } else {
                        console.log(`   ❌ INCONSISTÊNCIA: Access Token (${accessTokenEnv}) e Public Key (${publicKeyEnv}) de ambientes diferentes!`);
                    }
                }
                
            } else {
                console.log('❌ Configuração MercadoPago: Não encontrada');
            }
            
            console.log('─'.repeat(50));
        }

        // Verifica configurações específicas se Guild ID for fornecido
        const guildId = process.argv[2];
        if (guildId) {
            console.log(`\n🔍 Análise detalhada para Guild ID: ${guildId}`);
            
            const specificConfig = await BotConfig.findByGuild(guildId);
            if (specificConfig) {
                console.log('✅ Configuração encontrada');
                console.log('📄 Documento completo:');
                console.log(JSON.stringify(specificConfig.toObject(), null, 2));
            } else {
                console.log('❌ Configuração não encontrada para esta guild');
            }
        }

    } catch (error) {
        console.error('❌ Erro durante o debug:', error.message);
        console.error('Stack trace:', error.stack);
    } finally {
        await mongoose.disconnect();
        console.log('✅ Desconectado do MongoDB');
    }
}

/**
 * Função para testar salvamento de configuração
 */
async function testConfigSave() {
    try {
        console.log('🧪 Testando salvamento de configuração...');
        
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB');

        const testGuildId = 'test_guild_' + Date.now();
        const testUserId = 'test_user_123';

        // Cria configuração de teste
        let config = new BotConfig({ guildId: testGuildId });
        
        const testConfig = {
            accessToken: 'TEST-1234567890123456789012345678901234567890',
            publicKey: 'TEST-abcdef1234567890abcdef1234567890abcdef12',
            webhookSecret: 'test_webhook_secret_123',
            isEnabled: true
        };

        console.log('💾 Salvando configuração de teste...');
        await config.updateMercadoPago(testConfig, testUserId);
        
        console.log('✅ Configuração salva com sucesso');
        
        // Verifica se foi salva corretamente
        const savedConfig = await BotConfig.findByGuild(testGuildId);
        if (savedConfig && savedConfig.mercadoPago) {
            console.log('✅ Configuração recuperada do banco:');
            console.log(`   Access Token: ${savedConfig.mercadoPago.accessToken}`);
            console.log(`   Public Key: ${savedConfig.mercadoPago.publicKey}`);
            console.log(`   Webhook Secret: ${savedConfig.mercadoPago.webhookSecret}`);
            console.log(`   Habilitado: ${savedConfig.mercadoPago.isEnabled}`);
        } else {
            console.log('❌ Erro: Configuração não foi encontrada após salvamento');
        }
        
        // Remove configuração de teste
        await BotConfig.deleteOne({ guildId: testGuildId });
        console.log('🗑️ Configuração de teste removida');

    } catch (error) {
        console.error('❌ Erro no teste de salvamento:', error.message);
        console.error('Stack trace:', error.stack);
    } finally {
        await mongoose.disconnect();
        console.log('✅ Desconectado do MongoDB');
    }
}

// Executa o script
const command = process.argv[2];

if (command === 'test-save') {
    testConfigSave();
} else {
    debugMercadoPagoConfig();
}
