import { Events } from 'discord.js';
import { logger } from '../utils/logging/logger.js';

export default {
    name: Events.GuildDelete,
    async execute(guild) {
        try {
            await logger.system(`Bot removido de um servidor`, {
                guildId: guild.id
            }, {
                guild: guild.name || 'Nome não disponível',
                memberCount: guild.memberCount || 'Desconhecido',
                unavailable: guild.unavailable,
                reason: guild.unavailable ? 'Servidor indisponível' : 'Bot removido'
            });

        } catch (error) {
            await logger.logStructured('ERROR', 'EVENT', 'Erro no evento guildDelete', {
                guildId: guild?.id
            }, {
                error: error.message,
                stack: error.stack
            });
        }
    }
};
