import dotenv from 'dotenv';
import mongoose from 'mongoose';

// Carrega variáveis de ambiente
dotenv.config();

async function testConnection() {
    try {
        console.log('🔄 Testando conexão com MongoDB...');
        console.log('URI:', process.env.MONGODB_URI ? 'Definida' : 'Não definida');
        
        if (!process.env.MONGODB_URI) {
            throw new Error('MONGODB_URI não está definida');
        }

        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Conectado ao MongoDB com sucesso!');
        
        const db = mongoose.connection.db;
        const collections = await db.listCollections().toArray();
        
        console.log(`📋 Encontradas ${collections.length} coleções:`);
        collections.forEach(col => {
            console.log(`  - ${col.name}`);
        });
        
        // Verifica especificamente a coleção products
        const productsCollection = db.collection('products');
        const productCount = await productsCollection.countDocuments();
        console.log(`📦 Total de produtos: ${productCount}`);
        
        // Lista índices da coleção products
        const indexes = await productsCollection.indexes();
        console.log(`📊 Índices na coleção products: ${indexes.length}`);
        indexes.forEach(index => {
            console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
        });
        
        await mongoose.disconnect();
        console.log('✅ Desconectado do MongoDB');
        
    } catch (error) {
        console.error('❌ Erro:', error.message);
        process.exit(1);
    }
}

testConnection();
