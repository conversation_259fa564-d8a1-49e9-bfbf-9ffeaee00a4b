import { readdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { logger } from './logging/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Carrega todos os comandos das subpastas da pasta commands
 * @param {Client} client - Instância do cliente Discord
 */
export async function loadCommands(client) {
    try {
        const commandsPath = join(__dirname, '..', 'commands');
        let commandCount = 0;

        // Lê todas as categorias de comandos (subpastas)
        const commandCategories = readdirSync(commandsPath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);

        for (const category of commandCategories) {
            const categoryPath = join(commandsPath, category);
            
            // Lê todos os arquivos de comando da categoria
            const commandFiles = readdirSync(categoryPath)
                .filter(file => file.endsWith('.js'));

            for (const file of commandFiles) {
                try {
                    const filePath = join(categoryPath, file);
                    const fileUrl = `file://${filePath.replace(/\\/g, '/')}`;
                    
                    // Importa o comando dinamicamente
                    const command = await import(fileUrl);
                    
                    if (command.default && command.default.data && command.default.execute) {
                        // Adiciona o comando à coleção
                        client.commands.set(command.default.data.name, command.default);
                        commandCount++;
                        
                        logger.debug(`Comando carregado: ${command.default.data.name} (${category})`);
                    } else {
                        logger.warn(`⚠️ Comando inválido encontrado: ${file} - faltando propriedades obrigatórias`);
                    }
                } catch (error) {
                    logger.error(`❌ Erro ao carregar comando ${file}:`, error);
                }
            }
        }

        logger.info(`✅ ${commandCount} comandos carregados com sucesso`);
        
    } catch (error) {
        logger.error('❌ Erro ao carregar comandos:', error);
        throw error;
    }
}
