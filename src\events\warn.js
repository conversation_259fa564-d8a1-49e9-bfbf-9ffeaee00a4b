import { Events } from 'discord.js';
import { logger } from '../utils/logging/logger.js';

export default {
    name: Events.Warn,
    async execute(info) {
        try {
            await logger.logStructured('WARN', 'SYSTEM', 'Aviso do cliente Discord', {}, {
                warning: info,
                timestamp: new Date().toISOString()
            });

        } catch (logError) {
            // Fallback para console se o sistema de log falhar
            console.warn('Aviso do cliente Discord:', info);
            console.error('Erro ao fazer log do aviso:', logError);
        }
    }
};
