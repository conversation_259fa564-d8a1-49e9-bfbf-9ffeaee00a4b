import { Events } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';

export default {
    name: Events.GuildCreate,
    async execute(guild) {
        try {
            await logger.system(`Bot adicionado a um novo servidor`, {
                guildId: guild.id
            }, {
                guild: guild.name,
                memberCount: guild.memberCount,
                owner: guild.ownerId,
                createdAt: guild.createdAt.toISOString(),
                features: guild.features,
                boostLevel: guild.premiumTier,
                boostCount: guild.premiumSubscriptionCount
            });

            // Log adicional se for um servidor grande
            if (guild.memberCount > 1000) {
                await logger.system(`Bot adicionado a servidor grande`, {
                    guildId: guild.id
                }, {
                    guild: guild.name,
                    memberCount: guild.memberCount,
                    category: 'large_server'
                });
            }

        } catch (error) {
            await logger.logStructured('ERROR', 'EVENT', 'Erro no evento guildCreate', {
                guildId: guild?.id
            }, {
                error: error.message,
                stack: error.stack
            });
        }
    }
};
